"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/hooks/useChatHistory.ts":
/*!*************************************!*\
  !*** ./src/hooks/useChatHistory.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chatHistoryCache: () => (/* binding */ chatHistoryCache),\n/* harmony export */   useChatHistory: () => (/* binding */ useChatHistory),\n/* harmony export */   useChatHistoryPrefetch: () => (/* binding */ useChatHistoryPrefetch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useChatHistory,useChatHistoryPrefetch,chatHistoryCache auto */ \n// Global cache shared across all instances\nconst globalCache = new Map();\nconst cacheStats = {\n    hits: 0,\n    misses: 0\n};\n// Background prefetch queue\nconst prefetchQueue = new Set();\nlet isPrefetching = false;\nconst useChatHistory = (param)=>{\n    let { configId, enablePrefetch = true, cacheTimeout = 300000, staleTimeout = 30000 // 30 seconds\n     } = param;\n    const [chatHistory, setChatHistory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isStale, setIsStale] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const lastFetchRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Fetch function with aggressive caching\n    const fetchChatHistory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatHistory.useCallback[fetchChatHistory]\": async function(targetConfigId) {\n            let force = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, background = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n            const cacheKey = targetConfigId;\n            const cached = globalCache.get(cacheKey);\n            const now = Date.now();\n            // Return cached data if valid and not forced\n            if (!force && cached && now - cached.timestamp < cacheTimeout) {\n                cacheStats.hits++;\n                // Check if data is stale but still valid\n                const isDataStale = now - cached.timestamp > staleTimeout;\n                if (isDataStale && !cached.isStale) {\n                    // Mark as stale and trigger background refresh\n                    cached.isStale = true;\n                    if (enablePrefetch) {\n                        prefetchQueue.add(targetConfigId);\n                        processPrefetchQueue();\n                    }\n                }\n                return cached.data;\n            }\n            cacheStats.misses++;\n            // Cancel previous request if still pending\n            if (abortControllerRef.current) {\n                abortControllerRef.current.abort();\n            }\n            // Create new abort controller\n            abortControllerRef.current = new AbortController();\n            try {\n                // Determine if this is a workflow or router configuration\n                const isWorkflow = targetConfigId.startsWith('workflow_');\n                const actualId = isWorkflow ? targetConfigId.replace('workflow_', '') : targetConfigId;\n                const paramName = isWorkflow ? 'workflow_id' : 'custom_api_config_id';\n                const url = \"/api/chat/conversations?\".concat(paramName, \"=\").concat(actualId);\n                const response = await fetch(url, {\n                    signal: abortControllerRef.current.signal,\n                    headers: {\n                        'Cache-Control': 'no-cache',\n                        'X-Requested-With': 'XMLHttpRequest'\n                    }\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch chat history: \".concat(response.status, \" \").concat(response.statusText));\n                }\n                const conversations = await response.json();\n                // Update cache\n                globalCache.set(cacheKey, {\n                    data: conversations,\n                    timestamp: now,\n                    isStale: false\n                });\n                console.log(\"\\uD83D\\uDCE6 Chat history \".concat(background ? 'prefetched' : 'loaded', \" for config \").concat(targetConfigId, \": \").concat(conversations.length, \" conversations\"));\n                return conversations;\n            } catch (err) {\n                if (err.name === 'AbortError') {\n                    console.log('Chat history fetch aborted');\n                    throw err;\n                }\n                console.error('Error fetching chat history:', err);\n                // Return stale data if available\n                if (cached && cached.data.length > 0) {\n                    console.log('Returning stale chat history data due to fetch error');\n                    return cached.data;\n                }\n                throw err;\n            }\n        }\n    }[\"useChatHistory.useCallback[fetchChatHistory]\"], [\n        cacheTimeout,\n        staleTimeout,\n        enablePrefetch\n    ]);\n    // Background prefetch processor\n    const processPrefetchQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatHistory.useCallback[processPrefetchQueue]\": async ()=>{\n            if (isPrefetching || prefetchQueue.size === 0) return;\n            isPrefetching = true;\n            try {\n                const configsToFetch = Array.from(prefetchQueue);\n                prefetchQueue.clear();\n                for (const configId of configsToFetch){\n                    try {\n                        await fetchChatHistory(configId, true, true);\n                        // Small delay between prefetches\n                        await new Promise({\n                            \"useChatHistory.useCallback[processPrefetchQueue]\": (resolve)=>setTimeout(resolve, 100)\n                        }[\"useChatHistory.useCallback[processPrefetchQueue]\"]);\n                    } catch (error) {\n                        console.warn(\"Failed to prefetch chat history for config \".concat(configId, \":\"), error);\n                    }\n                }\n            } finally{\n                isPrefetching = false;\n            }\n        }\n    }[\"useChatHistory.useCallback[processPrefetchQueue]\"], [\n        fetchChatHistory\n    ]);\n    // Main refetch function\n    const refetch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatHistory.useCallback[refetch]\": async function() {\n            let force = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n            if (!configId) return;\n            // Optimistic update: show cached data immediately if available\n            const cached = globalCache.get(configId);\n            if (!force && cached && cached.data.length > 0) {\n                setChatHistory(cached.data);\n                setIsStale(cached.isStale);\n                setError(null);\n            }\n            setIsLoading(true);\n            lastFetchRef.current = configId;\n            try {\n                const conversations = await fetchChatHistory(configId, force);\n                // Only update if this is still the current config\n                if (lastFetchRef.current === configId) {\n                    setChatHistory(conversations);\n                    setIsStale(false);\n                    setError(null);\n                }\n            } catch (err) {\n                if (err.name !== 'AbortError' && lastFetchRef.current === configId) {\n                    setError(\"Failed to load chat history: \".concat(err.message));\n                }\n            } finally{\n                if (lastFetchRef.current === configId) {\n                    setIsLoading(false);\n                }\n            }\n        }\n    }[\"useChatHistory.useCallback[refetch]\"], [\n        configId,\n        fetchChatHistory\n    ]);\n    // Prefetch function for external use\n    const prefetch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatHistory.useCallback[prefetch]\": async (targetConfigId)=>{\n            if (!enablePrefetch) return;\n            prefetchQueue.add(targetConfigId);\n            processPrefetchQueue();\n        }\n    }[\"useChatHistory.useCallback[prefetch]\"], [\n        enablePrefetch,\n        processPrefetchQueue\n    ]);\n    // Cache invalidation\n    const invalidateCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatHistory.useCallback[invalidateCache]\": (targetConfigId)=>{\n            if (targetConfigId) {\n                globalCache.delete(targetConfigId);\n                console.log(\"\\uD83D\\uDDD1️ Invalidated chat history cache for config \".concat(targetConfigId));\n            } else {\n                globalCache.clear();\n                console.log('🗑️ Cleared all chat history cache');\n            }\n        }\n    }[\"useChatHistory.useCallback[invalidateCache]\"], []);\n    // Cache stats\n    const getCacheStats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatHistory.useCallback[getCacheStats]\": ()=>({\n                size: globalCache.size,\n                hits: cacheStats.hits,\n                misses: cacheStats.misses\n            })\n    }[\"useChatHistory.useCallback[getCacheStats]\"], []);\n    // Effect to load data when configId changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useChatHistory.useEffect\": ()=>{\n            if (configId) {\n                refetch();\n            } else {\n                setChatHistory([]);\n                setIsLoading(false);\n                setError(null);\n                setIsStale(false);\n            }\n        }\n    }[\"useChatHistory.useEffect\"], [\n        configId,\n        refetch\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useChatHistory.useEffect\": ()=>{\n            return ({\n                \"useChatHistory.useEffect\": ()=>{\n                    if (abortControllerRef.current) {\n                        abortControllerRef.current.abort();\n                    }\n                }\n            })[\"useChatHistory.useEffect\"];\n        }\n    }[\"useChatHistory.useEffect\"], []);\n    return {\n        chatHistory,\n        isLoading,\n        isStale,\n        error,\n        refetch,\n        prefetch,\n        invalidateCache,\n        getCacheStats\n    };\n};\n// Hook for prefetching chat history on navigation\nconst useChatHistoryPrefetch = ()=>{\n    const prefetchedConfigs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(new Set());\n    const prefetchChatHistory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatHistoryPrefetch.useCallback[prefetchChatHistory]\": async (configId)=>{\n            if (prefetchedConfigs.current.has(configId)) return;\n            prefetchedConfigs.current.add(configId);\n            prefetchQueue.add(configId);\n            // Process queue after a short delay to batch requests\n            setTimeout({\n                \"useChatHistoryPrefetch.useCallback[prefetchChatHistory]\": ()=>{\n                    if (prefetchQueue.size > 0) {\n                        const processPrefetch = {\n                            \"useChatHistoryPrefetch.useCallback[prefetchChatHistory].processPrefetch\": async ()=>{\n                                if (isPrefetching) return;\n                                isPrefetching = true;\n                                try {\n                                    const configsToFetch = Array.from(prefetchQueue);\n                                    prefetchQueue.clear();\n                                    for (const id of configsToFetch){\n                                        try {\n                                            const url = \"/api/chat/conversations?custom_api_config_id=\".concat(id);\n                                            const response = await fetch(url, {\n                                                headers: {\n                                                    'X-Prefetch': 'true'\n                                                }\n                                            });\n                                            if (response.ok) {\n                                                const conversations = await response.json();\n                                                globalCache.set(id, {\n                                                    data: conversations,\n                                                    timestamp: Date.now(),\n                                                    isStale: false\n                                                });\n                                                console.log(\"\\uD83D\\uDCE6 Prefetched chat history for config \".concat(id));\n                                            }\n                                        } catch (error) {\n                                            console.warn(\"Failed to prefetch chat history for \".concat(id, \":\"), error);\n                                        }\n                                        // Small delay between requests\n                                        await new Promise({\n                                            \"useChatHistoryPrefetch.useCallback[prefetchChatHistory].processPrefetch\": (resolve)=>setTimeout(resolve, 100)\n                                        }[\"useChatHistoryPrefetch.useCallback[prefetchChatHistory].processPrefetch\"]);\n                                    }\n                                } finally{\n                                    isPrefetching = false;\n                                }\n                            }\n                        }[\"useChatHistoryPrefetch.useCallback[prefetchChatHistory].processPrefetch\"];\n                        processPrefetch();\n                    }\n                }\n            }[\"useChatHistoryPrefetch.useCallback[prefetchChatHistory]\"], 200);\n        }\n    }[\"useChatHistoryPrefetch.useCallback[prefetchChatHistory]\"], []);\n    return {\n        prefetchChatHistory\n    };\n};\n// Export cache utilities for debugging\nconst chatHistoryCache = {\n    getCache: ()=>globalCache,\n    getStats: ()=>cacheStats,\n    clear: ()=>{\n        globalCache.clear();\n        cacheStats.hits = 0;\n        cacheStats.misses = 0;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useChatHistory.ts\n"));

/***/ })

});