import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { workflowExecutor } from '@/lib/workflow/WorkflowExecutor';
import { WorkflowNode } from '@/types/manualBuild';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { workflowId, nodes, edges, userInput } = body;

    if (!workflowId || !nodes || !edges) {
      return NextResponse.json({ 
        error: 'Missing required fields: workflowId, nodes, edges' 
      }, { status: 400 });
    }

    console.log(`🚀 Executing workflow ${workflowId} for user ${user.id}`);
    console.log(`📊 Workflow contains ${nodes.length} nodes and ${edges.length} edges`);

    // Execute the workflow
    const result = await workflowExecutor.executeWorkflow(
      workflowId,
      user.id,
      nodes as WorkflowNode[],
      edges,
      userInput
    );

    return NextResponse.json({
      success: true,
      workflowId,
      result,
      executedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Workflow execution error:', error);
    
    return NextResponse.json({
      error: 'Workflow execution failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const workflowId = searchParams.get('workflowId');

    if (workflowId) {
      // Get specific workflow execution status
      const execution = workflowExecutor.getExecutionStatus(workflowId);
      
      if (!execution) {
        return NextResponse.json({ error: 'Workflow not found' }, { status: 404 });
      }

      // Only return execution if it belongs to the authenticated user
      if (execution.userId !== user.id) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
      }

      return NextResponse.json(execution);
    } else {
      // Get all active executions for the user
      const allExecutions = workflowExecutor.getActiveExecutions();
      const userExecutions = allExecutions.filter(exec => exec.userId === user.id);

      return NextResponse.json({
        executions: userExecutions,
        totalActive: userExecutions.length
      });
    }

  } catch (error) {
    console.error('Get workflow execution error:', error);
    
    return NextResponse.json({
      error: 'Failed to get workflow execution',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
