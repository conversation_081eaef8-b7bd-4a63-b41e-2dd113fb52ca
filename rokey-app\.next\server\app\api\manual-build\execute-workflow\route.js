/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/manual-build/execute-workflow/route";
exports.ids = ["app/api/manual-build/execute-workflow/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmanual-build%2Fexecute-workflow%2Froute&page=%2Fapi%2Fmanual-build%2Fexecute-workflow%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmanual-build%2Fexecute-workflow%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmanual-build%2Fexecute-workflow%2Froute&page=%2Fapi%2Fmanual-build%2Fexecute-workflow%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmanual-build%2Fexecute-workflow%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_manual_build_execute_workflow_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/manual-build/execute-workflow/route.ts */ \"(rsc)/./src/app/api/manual-build/execute-workflow/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/manual-build/execute-workflow/route\",\n        pathname: \"/api/manual-build/execute-workflow\",\n        filename: \"route\",\n        bundlePath: \"app/api/manual-build/execute-workflow/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\manual-build\\\\execute-workflow\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_manual_build_execute_workflow_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmanual-build%2Fexecute-workflow%2Froute&page=%2Fapi%2Fmanual-build%2Fexecute-workflow%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmanual-build%2Fexecute-workflow%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/manual-build/execute-workflow/route.ts":
/*!************************************************************!*\
  !*** ./src/app/api/manual-build/execute-workflow/route.ts ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_workflow_WorkflowExecutor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/workflow/WorkflowExecutor */ \"(rsc)/./src/lib/workflow/WorkflowExecutor.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientOnRequest)();\n        // Get authenticated user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { workflowId, nodes, edges, userInput } = body;\n        if (!workflowId || !nodes || !edges) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields: workflowId, nodes, edges'\n            }, {\n                status: 400\n            });\n        }\n        console.log(`🚀 Executing workflow ${workflowId} for user ${user.id}`);\n        console.log(`📊 Workflow contains ${nodes.length} nodes and ${edges.length} edges`);\n        // Execute the workflow\n        const result = await _lib_workflow_WorkflowExecutor__WEBPACK_IMPORTED_MODULE_2__.workflowExecutor.executeWorkflow(workflowId, user.id, nodes, edges, userInput);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            workflowId,\n            result,\n            executedAt: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('Workflow execution error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Workflow execution failed',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientOnRequest)();\n        // Get authenticated user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const workflowId = searchParams.get('workflowId');\n        if (workflowId) {\n            // Get specific workflow execution status\n            const execution = _lib_workflow_WorkflowExecutor__WEBPACK_IMPORTED_MODULE_2__.workflowExecutor.getExecutionStatus(workflowId);\n            if (!execution) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Workflow not found'\n                }, {\n                    status: 404\n                });\n            }\n            // Only return execution if it belongs to the authenticated user\n            if (execution.userId !== user.id) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Unauthorized'\n                }, {\n                    status: 403\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(execution);\n        } else {\n            // Get all active executions for the user\n            const allExecutions = _lib_workflow_WorkflowExecutor__WEBPACK_IMPORTED_MODULE_2__.workflowExecutor.getActiveExecutions();\n            const userExecutions = allExecutions.filter((exec)=>exec.userId === user.id);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                executions: userExecutions,\n                totalActive: userExecutions.length\n            });\n        }\n    } catch (error) {\n        console.error('Get workflow execution error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to get workflow execution',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/manual-build/execute-workflow/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/browserless.ts":
/*!********************************!*\
  !*** ./src/lib/browserless.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Browserless.io API service with key rotation\n// Similar to Jina API key rotation system\nclass BrowserlessService {\n    constructor(){\n        this.apiKeys = [];\n        this.currentKeyIndex = 0;\n        this.keyUsageCount = new Map();\n        this.keyErrors = new Map();\n        this.MAX_RETRIES = 3;\n        this.ERROR_THRESHOLD = 5;\n        this.ENDPOINT = 'https://production-sfo.browserless.io';\n        this.initializeKeys();\n    }\n    static getInstance() {\n        if (!BrowserlessService.instance) {\n            BrowserlessService.instance = new BrowserlessService();\n        }\n        return BrowserlessService.instance;\n    }\n    initializeKeys() {\n        const keysString = process.env.BROWSERLESS_API_KEYS;\n        if (!keysString) {\n            console.error('BROWSERLESS_API_KEYS not found in environment variables');\n            return;\n        }\n        this.apiKeys = keysString.split(',').map((key)=>key.trim()).filter(Boolean);\n        console.log(`Initialized Browserless service with ${this.apiKeys.length} API keys`);\n        // Initialize usage tracking\n        this.apiKeys.forEach((key)=>{\n            this.keyUsageCount.set(key, 0);\n            this.keyErrors.set(key, 0);\n        });\n    }\n    getNextApiKey() {\n        if (this.apiKeys.length === 0) {\n            throw new Error('No Browserless API keys available');\n        }\n        // Find the key with lowest usage and errors\n        let bestKey = this.apiKeys[0];\n        let bestScore = this.calculateKeyScore(bestKey);\n        for (const key of this.apiKeys){\n            const score = this.calculateKeyScore(key);\n            if (score < bestScore) {\n                bestKey = key;\n                bestScore = score;\n            }\n        }\n        return bestKey;\n    }\n    calculateKeyScore(key) {\n        const usage = this.keyUsageCount.get(key) || 0;\n        const errors = this.keyErrors.get(key) || 0;\n        // Higher score = worse key (more usage + more errors)\n        return usage + errors * 10;\n    }\n    incrementKeyUsage(key) {\n        const currentUsage = this.keyUsageCount.get(key) || 0;\n        this.keyUsageCount.set(key, currentUsage + 1);\n    }\n    incrementKeyError(key) {\n        const currentErrors = this.keyErrors.get(key) || 0;\n        this.keyErrors.set(key, currentErrors + 1);\n    }\n    isKeyHealthy(key) {\n        const errors = this.keyErrors.get(key) || 0;\n        return errors < this.ERROR_THRESHOLD;\n    }\n    getHealthyKeys() {\n        return this.apiKeys.filter((key)=>this.isKeyHealthy(key));\n    }\n    async executeFunction(code, context, config) {\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            // Reset error counts if all keys are unhealthy\n            this.keyErrors.clear();\n            this.apiKeys.forEach((key)=>this.keyErrors.set(key, 0));\n            console.log('All Browserless keys were unhealthy, resetting error counts');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.MAX_RETRIES; attempt++){\n            try {\n                const apiKey = this.getNextApiKey();\n                this.incrementKeyUsage(apiKey);\n                const response = await this.makeRequest(apiKey, code, context, config);\n                // Success - return the response\n                return response;\n            } catch (error) {\n                lastError = error;\n                console.error(`Browserless attempt ${attempt + 1} failed:`, error);\n                // If it's a rate limit or quota error, mark the key as having an error\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All Browserless API attempts failed');\n    }\n    async makeRequest(apiKey, code, context, config) {\n        const url = `${this.ENDPOINT}/function?token=${apiKey}`;\n        const requestBody = context ? {\n            code,\n            context\n        } : code;\n        const headers = {\n            'Content-Type': context ? 'application/json' : 'application/javascript',\n            'User-Agent': config?.userAgent || 'RouKey-Browser-Agent/1.0'\n        };\n        const response = await fetch(url, {\n            method: 'POST',\n            headers,\n            body: context ? JSON.stringify(requestBody) : code,\n            signal: AbortSignal.timeout(config?.timeout || 30000)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(`Browserless API error: ${response.status} - ${errorText}`);\n        }\n        const result = await response.json();\n        return result;\n    }\n    isRateLimitError(error) {\n        const message = error.message.toLowerCase();\n        return message.includes('rate limit') || message.includes('quota') || message.includes('429') || message.includes('too many requests');\n    }\n    // Convenience methods for common browser tasks\n    async navigateAndExtract(url, selector) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n        \n        const title = await page.title();\n        const content = ${selector ? `await page.$eval(\"${selector}\", el => el.textContent || el.innerText)` : 'await page.evaluate(() => document.body.innerText)'};\n        \n        return {\n          data: {\n            url: \"${url}\",\n            title,\n            content: content?.trim() || \"\"\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    async searchAndExtract(query, searchEngine = 'google') {\n        const searchUrl = searchEngine === 'google' ? `https://www.google.com/search?q=${encodeURIComponent(query)}` : `https://www.bing.com/search?q=${encodeURIComponent(query)}`;\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${searchUrl}\", { waitUntil: 'networkidle0' });\n        \n        // Wait for search results to load\n        await page.waitForSelector('${searchEngine === 'google' ? '[data-ved]' : '.b_algo'}', { timeout: 10000 });\n        \n        const results = await page.evaluate(() => {\n          const selector = '${searchEngine === 'google' ? '[data-ved] h3' : '.b_algo h2'}';\n          const elements = document.querySelectorAll(selector);\n          \n          return Array.from(elements).slice(0, 5).map(el => ({\n            title: el.textContent?.trim() || '',\n            link: el.closest('a')?.href || ''\n          }));\n        });\n        \n        return {\n          data: {\n            query: \"${query}\",\n            searchEngine: \"${searchEngine}\",\n            results\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    async takeScreenshot(url, options) {\n        const fullPage = options?.fullPage ?? false;\n        const selector = options?.selector || '';\n        const quality = options?.quality || 80;\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        let screenshot;\n        if (\"${selector}\") {\n          // Screenshot specific element\n          const element = await page.waitForSelector(\"${selector}\", { timeout: 10000 });\n          screenshot = await element.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n        } else {\n          // Screenshot full page or viewport\n          screenshot = await page.screenshot({\n            encoding: 'base64',\n            fullPage: ${fullPage},\n            type: 'png',\n            quality: ${quality}\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            screenshot: screenshot,\n            selector: \"${selector}\",\n            fullPage: ${fullPage},\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Advanced form filling with intelligent field detection\n   */ async fillForm(url, formData, options) {\n        const submitAfterFill = options?.submitAfterFill ?? false;\n        const waitForNavigation = options?.waitForNavigation ?? false;\n        const formSelector = options?.formSelector || 'form';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const formData = ${JSON.stringify(formData)};\n        const results = [];\n\n        // Wait for form to be present\n        await page.waitForSelector(\"${formSelector}\", { timeout: 10000 });\n\n        // Fill each field intelligently\n        for (const [fieldName, value] of Object.entries(formData)) {\n          try {\n            // Try multiple selector strategies\n            const selectors = [\n              \\`input[name=\"\\${fieldName}\"]\\`,\n              \\`input[id=\"\\${fieldName}\"]\\`,\n              \\`textarea[name=\"\\${fieldName}\"]\\`,\n              \\`select[name=\"\\${fieldName}\"]\\`,\n              \\`input[placeholder*=\"\\${fieldName}\"]\\`,\n              \\`input[aria-label*=\"\\${fieldName}\"]\\`,\n              \\`[data-testid=\"\\${fieldName}\"]\\`\n            ];\n\n            let filled = false;\n            for (const selector of selectors) {\n              const elements = await page.$$(selector);\n              if (elements.length > 0) {\n                const element = elements[0];\n                const tagName = await element.evaluate(el => el.tagName.toLowerCase());\n\n                if (tagName === 'select') {\n                  await element.selectOption(value.toString());\n                } else if (tagName === 'input') {\n                  const inputType = await element.getAttribute('type');\n                  if (inputType === 'checkbox' || inputType === 'radio') {\n                    if (value) await element.check();\n                  } else {\n                    await element.fill(value.toString());\n                  }\n                } else {\n                  await element.fill(value.toString());\n                }\n\n                results.push({\n                  field: fieldName,\n                  selector: selector,\n                  value: value,\n                  success: true\n                });\n                filled = true;\n                break;\n              }\n            }\n\n            if (!filled) {\n              results.push({\n                field: fieldName,\n                value: value,\n                success: false,\n                error: 'Field not found'\n              });\n            }\n          } catch (error) {\n            results.push({\n              field: fieldName,\n              value: value,\n              success: false,\n              error: error.message\n            });\n          }\n        }\n\n        let submitResult = null;\n        if (${submitAfterFill}) {\n          try {\n            const submitButton = await page.$('input[type=\"submit\"], button[type=\"submit\"], button:has-text(\"Submit\")');\n            if (submitButton) {\n              ${waitForNavigation ? 'await Promise.all([page.waitForNavigation(), submitButton.click()]);' : 'await submitButton.click();'}\n              submitResult = { success: true, message: 'Form submitted successfully' };\n            } else {\n              submitResult = { success: false, error: 'Submit button not found' };\n            }\n          } catch (error) {\n            submitResult = { success: false, error: error.message };\n          }\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            formFillResults: results,\n            submitResult: submitResult,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * CAPTCHA solving with multiple strategies\n   */ async solveCaptcha(url, captchaType = 'recaptcha') {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const captchaType = \"${captchaType}\";\n        let result = { success: false, type: captchaType };\n\n        try {\n          if (captchaType === 'recaptcha') {\n            // Look for reCAPTCHA\n            const recaptcha = await page.$('.g-recaptcha, [data-sitekey]');\n            if (recaptcha) {\n              // For now, we'll detect and report the presence\n              // In production, integrate with 2captcha or similar service\n              const sitekey = await recaptcha.getAttribute('data-sitekey');\n              result = {\n                success: false,\n                type: 'recaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'reCAPTCHA detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'hcaptcha') {\n            // Look for hCaptcha\n            const hcaptcha = await page.$('.h-captcha, [data-hcaptcha-sitekey]');\n            if (hcaptcha) {\n              const sitekey = await hcaptcha.getAttribute('data-hcaptcha-sitekey');\n              result = {\n                success: false,\n                type: 'hcaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'hCaptcha detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'text') {\n            // Look for text-based CAPTCHA\n            const textCaptcha = await page.$('img[src*=\"captcha\"], img[alt*=\"captcha\"], .captcha-image');\n            if (textCaptcha) {\n              result = {\n                success: false,\n                type: 'text',\n                detected: true,\n                message: 'Text CAPTCHA detected but solving not implemented yet'\n              };\n            }\n          }\n\n          // If no CAPTCHA detected\n          if (!result.detected) {\n            result = {\n              success: true,\n              type: captchaType,\n              detected: false,\n              message: 'No CAPTCHA detected on page'\n            };\n          }\n        } catch (error) {\n          result = {\n            success: false,\n            type: captchaType,\n            error: error.message\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            captchaResult: result,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Execute custom JavaScript with advanced capabilities\n   */ async executeAdvancedScript(url, script, options) {\n        const waitForSelector = options?.waitForSelector || '';\n        const timeout = options?.timeout || 30000;\n        const returnType = options?.returnType || 'json';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        ${waitForSelector ? `await page.waitForSelector(\"${waitForSelector}\", { timeout: ${timeout} });` : ''}\n\n        // Execute custom script\n        const scriptResult = await page.evaluate(() => {\n          ${script}\n        });\n\n        let finalResult = scriptResult;\n\n        if (\"${returnType}\" === 'screenshot') {\n          const screenshot = await page.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n          finalResult = {\n            scriptResult: scriptResult,\n            screenshot: screenshot\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            result: finalResult,\n            returnType: \"${returnType}\",\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Smart content extraction with multiple strategies\n   */ async smartExtract(url, extractionGoals) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const goals = ${JSON.stringify(extractionGoals)};\n        const results = {};\n\n        // Common extraction patterns\n        const extractors = {\n          prices: () => {\n            const priceSelectors = [\n              '[class*=\"price\"]', '[id*=\"price\"]', '.cost', '.amount',\n              '[data-testid*=\"price\"]', '.currency', '[class*=\"dollar\"]'\n            ];\n            const prices = [];\n            priceSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const text = el.textContent?.trim();\n                if (text && /[$£€¥₹]|\\\\d+\\\\.\\\\d{2}/.test(text)) {\n                  prices.push({\n                    text: text,\n                    selector: selector,\n                    element: el.tagName\n                  });\n                }\n              });\n            });\n            return prices;\n          },\n\n          contact: () => {\n            const contactSelectors = [\n              '[href^=\"mailto:\"]', '[href^=\"tel:\"]', '.contact', '.email', '.phone'\n            ];\n            const contacts = [];\n            contactSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                contacts.push({\n                  text: el.textContent?.trim(),\n                  href: el.getAttribute('href'),\n                  type: el.getAttribute('href')?.startsWith('mailto:') ? 'email' : 'phone'\n                });\n              });\n            });\n            return contacts;\n          },\n\n          products: () => {\n            const productSelectors = [\n              '.product', '[class*=\"product\"]', '.item', '[data-testid*=\"product\"]'\n            ];\n            const products = [];\n            productSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const title = el.querySelector('h1, h2, h3, .title, [class*=\"title\"]')?.textContent?.trim();\n                const price = el.querySelector('[class*=\"price\"], .cost')?.textContent?.trim();\n                const image = el.querySelector('img')?.src;\n                if (title) {\n                  products.push({ title, price, image });\n                }\n              });\n            });\n            return products;\n          },\n\n          text: () => {\n            // Extract main content\n            const contentSelectors = ['main', 'article', '.content', '#content', '.post'];\n            let content = '';\n            for (const selector of contentSelectors) {\n              const el = document.querySelector(selector);\n              if (el) {\n                content = el.textContent?.trim() || '';\n                break;\n              }\n            }\n            if (!content) {\n              content = document.body.textContent?.trim() || '';\n            }\n            return content.substring(0, 5000); // Limit to 5000 chars\n          },\n\n          links: () => {\n            const links = [];\n            document.querySelectorAll('a[href]').forEach(el => {\n              const href = el.getAttribute('href');\n              const text = el.textContent?.trim();\n              if (href && text && !href.startsWith('#')) {\n                links.push({\n                  url: new URL(href, window.location.href).href,\n                  text: text\n                });\n              }\n            });\n            return links.slice(0, 50); // Limit to 50 links\n          }\n        };\n\n        // Execute extractors based on goals\n        goals.forEach(goal => {\n          const goalLower = goal.toLowerCase();\n          if (goalLower.includes('price') || goalLower.includes('cost')) {\n            results.prices = extractors.prices();\n          }\n          if (goalLower.includes('contact') || goalLower.includes('email') || goalLower.includes('phone')) {\n            results.contact = extractors.contact();\n          }\n          if (goalLower.includes('product') || goalLower.includes('item')) {\n            results.products = extractors.products();\n          }\n          if (goalLower.includes('text') || goalLower.includes('content')) {\n            results.text = extractors.text();\n          }\n          if (goalLower.includes('link') || goalLower.includes('url')) {\n            results.links = extractors.links();\n          }\n        });\n\n        // If no specific goals, extract everything\n        if (goals.length === 0) {\n          Object.keys(extractors).forEach(key => {\n            results[key] = extractors[key]();\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            extractionGoals: goals,\n            results: results,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    // Get service statistics\n    getStats() {\n        return {\n            totalKeys: this.apiKeys.length,\n            healthyKeys: this.getHealthyKeys().length,\n            keyUsage: Object.fromEntries(this.keyUsageCount),\n            keyErrors: Object.fromEntries(this.keyErrors)\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowserlessService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browserless.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/intelligentBrowsing.ts":
/*!****************************************!*\
  !*** ./src/lib/intelligentBrowsing.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntelligentBrowsingService: () => (/* binding */ IntelligentBrowsingService)\n/* harmony export */ });\n/* harmony import */ var _browserless__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./browserless */ \"(rsc)/./src/lib/browserless.ts\");\n/* harmony import */ var _memory_MemoryService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./memory/MemoryService */ \"(rsc)/./src/lib/memory/MemoryService.ts\");\n// Intelligent Browsing Service\n// Handles complex multi-step browsing tasks with AI planning and memory\n\n\nclass IntelligentBrowsingService {\n    constructor(){\n        this.activeTasks = new Map();\n        this.memoryNodeId = null;\n        this.workflowId = null;\n        this.userId = null;\n        this.browserless = _browserless__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance();\n    }\n    static getInstance() {\n        if (!IntelligentBrowsingService.instance) {\n            IntelligentBrowsingService.instance = new IntelligentBrowsingService();\n        }\n        return IntelligentBrowsingService.instance;\n    }\n    /**\n   * Connect to a Memory node for persistent storage\n   */ connectMemory(memoryNodeId, workflowId, userId) {\n        this.memoryNodeId = memoryNodeId;\n        this.workflowId = workflowId;\n        this.userId = userId;\n        console.log(`🧠 Browsing connected to Memory node: ${memoryNodeId}`);\n    }\n    // Execute a browsing plan with memory tracking and smart completion detection\n    async executeBrowsingPlan(plan, memory, config = {}, plannerNodeId, aiProviderConfig) {\n        console.log(`🌐 Starting intelligent browsing task: ${plan.task}`);\n        // Load existing memory if connected to Memory node\n        if (this.memoryNodeId && this.workflowId && this.userId) {\n            const existingMemory = await _memory_MemoryService__WEBPACK_IMPORTED_MODULE_1__.memoryService.retrieve('browsing_memory', this.memoryNodeId, this.workflowId, this.userId);\n            if (existingMemory) {\n                console.log('🧠 Loaded existing browsing memory');\n                // Merge existing memory with current memory\n                memory.completedSubtasks = [\n                    ...existingMemory.completedSubtasks || [],\n                    ...memory.completedSubtasks\n                ];\n                memory.visitedUrls = [\n                    ...existingMemory.visitedUrls || [],\n                    ...memory.visitedUrls\n                ];\n                memory.searchQueries = [\n                    ...existingMemory.searchQueries || [],\n                    ...memory.searchQueries\n                ];\n                memory.gatheredData = {\n                    ...existingMemory.gatheredData || {},\n                    ...memory.gatheredData\n                };\n                memory.searchResults = [\n                    ...existingMemory.searchResults || [],\n                    ...memory.searchResults\n                ];\n                memory.selectedWebsites = [\n                    ...existingMemory.selectedWebsites || [],\n                    ...memory.selectedWebsites\n                ];\n                memory.completionStatus = existingMemory.completionStatus || 'insufficient';\n            }\n        }\n        // If we have a planner node, get an enhanced plan\n        if (plannerNodeId && aiProviderConfig) {\n            console.log('📋 Using planner node to create detailed browsing plan');\n            plan = await this.createDetailedPlanWithAI(plan.task, aiProviderConfig, config);\n        }\n        // Update memory with task start\n        memory.lastUpdate = new Date().toISOString();\n        memory.completionStatus = 'insufficient';\n        this.activeTasks.set(plan.id, memory);\n        let finalResult = null;\n        const results = [];\n        try {\n            for (const subtask of plan.subtasks){\n                // Skip if already completed\n                if (memory.completedSubtasks.includes(subtask.id)) {\n                    console.log(`Skipping completed subtask: ${subtask.description}`);\n                    continue;\n                }\n                // Check if we have sufficient information to complete the task early\n                if (await this.checkTaskCompletion(memory, plan.task)) {\n                    console.log(`✅ Task completion detected early. Skipping remaining subtasks.`);\n                    memory.completionStatus = 'complete';\n                    // Mark remaining subtasks as skipped\n                    const remainingSubtasks = plan.subtasks.filter((st)=>!memory.completedSubtasks.includes(st.id) && st.id !== subtask.id);\n                    remainingSubtasks.forEach((st)=>st.status = 'skipped');\n                    break;\n                }\n                console.log(`Executing subtask: ${subtask.description}`);\n                subtask.status = 'in_progress';\n                const startTime = Date.now();\n                try {\n                    const result = await this.executeSubtask(subtask, memory, config);\n                    subtask.status = 'completed';\n                    subtask.result = result;\n                    subtask.executionTime = Date.now() - startTime;\n                    // Update memory\n                    memory.completedSubtasks.push(subtask.id);\n                    memory.gatheredData[subtask.id] = result;\n                    memory.lastUpdate = new Date().toISOString();\n                    // Update completion status based on gathered data\n                    memory.completionStatus = this.assessCompletionStatus(memory, plan.task);\n                    // Save progress to memory after each subtask\n                    await this.saveMemoryToPersistentStorage(memory);\n                    results.push(result);\n                    console.log(`✅ Completed subtask: ${subtask.description} (Status: ${memory.completionStatus})`);\n                    // Check for early completion\n                    if (memory.completionStatus === 'sufficient' || memory.completionStatus === 'complete') {\n                        console.log('🎯 Early completion detected - sufficient information gathered');\n                        break;\n                    }\n                } catch (error) {\n                    subtask.status = 'failed';\n                    subtask.error = error instanceof Error ? error.message : 'Unknown error';\n                    subtask.executionTime = Date.now() - startTime;\n                    console.error(`Failed subtask: ${subtask.description}`, error);\n                    // Decide whether to continue or abort based on subtask importance\n                    if (subtask.type === 'search' || subtask.type === 'navigate') {\n                        // Critical subtasks - abort if they fail\n                        throw error;\n                    }\n                // Non-critical subtasks - continue with warning\n                }\n            }\n            // Synthesize final result from all gathered data\n            finalResult = this.synthesizeResults(results, plan.task);\n            memory.isComplete = true;\n            memory.finalResult = finalResult;\n            memory.lastUpdate = new Date().toISOString();\n            console.log(`Completed browsing task: ${plan.task}`);\n        } catch (error) {\n            console.error(`Browsing task failed: ${plan.task}`, error);\n            memory.lastUpdate = new Date().toISOString();\n            throw error;\n        } finally{\n            this.activeTasks.set(plan.id, memory);\n            // Save memory to Memory node if connected\n            if (this.memoryNodeId && this.workflowId && this.userId) {\n                await this.saveMemoryToPersistentStorage(memory);\n            }\n        }\n        return {\n            memory,\n            result: finalResult\n        };\n    }\n    // Execute individual subtask\n    async executeSubtask(subtask, memory, config) {\n        console.log(`🔄 Executing ${subtask.type} subtask: ${subtask.description}`);\n        switch(subtask.type){\n            case 'search':\n                return this.executeSearch(subtask, memory, config);\n            case 'navigate':\n                return this.executeNavigate(subtask, memory, config);\n            case 'extract':\n                return this.executeExtract(subtask, memory, config);\n            case 'screenshot':\n                return this.executeScreenshot(subtask, memory, config);\n            case 'form_fill':\n                return this.executeFormFill(subtask, memory, config);\n            case 'click':\n                return this.executeClick(subtask, memory, config);\n            case 'wait':\n                return this.executeWait(subtask, memory, config);\n            case 'analyze_snippets':\n                return this.executeSnippetAnalysis(subtask, memory, config);\n            case 'analyze_results':\n                return this.executeResultAnalysis(subtask, memory, config);\n            case 'check_completion':\n                return this.executeCompletionCheck(subtask, memory, config);\n            default:\n                throw new Error(`Unknown subtask type: ${subtask.type}`);\n        }\n    }\n    async executeSearch(subtask, memory, config) {\n        const query = subtask.target || '';\n        const searchEngine = config.searchEngines?.[0] || 'google';\n        memory.searchQueries.push(query);\n        const result = await this.browserless.searchAndExtract(query, searchEngine);\n        // Store search results with snippets for analysis\n        if (result.data && Array.isArray(result.data)) {\n            memory.searchResults.push(...result.data.map((item)=>({\n                    ...item,\n                    query,\n                    searchEngine,\n                    timestamp: new Date().toISOString()\n                })));\n        }\n        return result.data;\n    }\n    async executeNavigate(subtask, memory, config) {\n        console.log('🌐 Navigating to selected websites for detailed extraction');\n        // If target is 'selected_websites', use the websites selected from analysis\n        let urlsToVisit = [];\n        if (subtask.target === 'selected_websites') {\n            urlsToVisit = memory.selectedWebsites || [];\n            if (urlsToVisit.length === 0) {\n                throw new Error('No websites selected for navigation');\n            }\n        } else {\n            urlsToVisit = [\n                subtask.target || ''\n            ];\n        }\n        const results = [];\n        const selector = subtask.parameters?.selector;\n        // Visit each selected website\n        for (const url of urlsToVisit){\n            try {\n                console.log(`📄 Visiting: ${url}`);\n                memory.visitedUrls.push(url);\n                const result = await this.browserless.navigateAndExtract(url, selector);\n                if (result.data) {\n                    results.push({\n                        url,\n                        content: result.data,\n                        timestamp: new Date().toISOString(),\n                        success: true\n                    });\n                    // Store individual site data\n                    memory.gatheredData[url] = result.data;\n                }\n            } catch (error) {\n                console.error(`❌ Failed to visit ${url}:`, error);\n                results.push({\n                    url,\n                    error: error instanceof Error ? error.message : 'Unknown error',\n                    timestamp: new Date().toISOString(),\n                    success: false\n                });\n            }\n        }\n        console.log(`✅ Visited ${results.length} websites, ${results.filter((r)=>r.success).length} successful`);\n        return {\n            visitedSites: results,\n            successfulVisits: results.filter((r)=>r.success).length,\n            totalAttempts: results.length\n        };\n    }\n    async executeExtract(subtask, memory, config) {\n        // Custom extraction logic using Browserless function\n        const url = subtask.target || '';\n        const selector = subtask.parameters?.selector || 'body';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n        \n        const elements = await page.$$eval(\"${selector}\", els => \n          els.map(el => ({\n            text: el.textContent?.trim() || '',\n            html: el.innerHTML,\n            attributes: Object.fromEntries(\n              Array.from(el.attributes).map(attr => [attr.name, attr.value])\n            )\n          }))\n        );\n        \n        return {\n          data: {\n            url: \"${url}\",\n            selector: \"${selector}\",\n            elements,\n            extractedAt: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        const result = await this.browserless.executeFunction(code);\n        return result.data;\n    }\n    async executeScreenshot(subtask, memory, config) {\n        const url = subtask.target || '';\n        const result = await this.browserless.takeScreenshot(url);\n        if (result.data.screenshot) {\n            memory.screenshots.push(result.data.screenshot);\n        }\n        return result.data;\n    }\n    async executeFormFill(subtask, memory, config) {\n        // Form filling logic - to be implemented\n        throw new Error('Form filling not yet implemented');\n    }\n    async executeClick(subtask, memory, config) {\n        // Click logic - to be implemented\n        throw new Error('Click actions not yet implemented');\n    }\n    async executeWait(subtask, memory, config) {\n        const waitTime = subtask.parameters?.duration || 1000;\n        await new Promise((resolve)=>setTimeout(resolve, waitTime));\n        return {\n            waited: waitTime\n        };\n    }\n    // Synthesize final result from all gathered data\n    synthesizeResults(results, originalTask) {\n        // Find the memory for this task\n        let memory;\n        for (const [taskId, taskMemory] of this.activeTasks.entries()){\n            if (taskMemory.taskId.includes(originalTask.substring(0, 20))) {\n                memory = taskMemory;\n                break;\n            }\n        }\n        if (!memory) {\n            // Fallback if memory not found\n            return {\n                task: originalTask,\n                summary: `Completed browsing task with ${results.length} operations`,\n                data: results,\n                synthesizedAt: new Date().toISOString(),\n                totalOperations: results.length\n            };\n        }\n        // Extract key information from all results\n        const searchResults = results.filter((r)=>r && Array.isArray(r));\n        const navigationResults = results.filter((r)=>r && r.visitedSites);\n        const analysisResults = results.filter((r)=>r && r.selectedWebsites);\n        // Compile comprehensive summary\n        const summary = {\n            task: originalTask,\n            status: memory.completionStatus,\n            totalOperations: results.length,\n            searchesPerformed: memory.searchQueries.length,\n            sitesVisited: memory.visitedUrls.length,\n            successfulExtractions: Object.keys(memory.gatheredData).length,\n            contentQuality: this.assessContentQuality(memory),\n            // Key findings\n            keyFindings: this.extractKeyFindings(memory, originalTask),\n            // Source information\n            sources: memory.visitedUrls.map((url)=>({\n                    url,\n                    hasData: !!memory.gatheredData[url],\n                    timestamp: memory.lastUpdate\n                })),\n            // Raw data for further processing\n            rawData: memory.gatheredData,\n            metadata: {\n                executionTime: Date.now() - new Date(memory.lastUpdate).getTime(),\n                timestamp: new Date().toISOString(),\n                memoryId: memory.taskId\n            }\n        };\n        console.log(`📋 Synthesized final result: ${summary.keyFindings.length} key findings from ${summary.sitesVisited} sites`);\n        return summary;\n    }\n    // Extract key findings from gathered data\n    extractKeyFindings(memory, originalTask) {\n        const findings = [];\n        // Extract findings from successful site visits\n        for (const [url, data] of Object.entries(memory.gatheredData)){\n            if (data && typeof data === 'object') {\n                if (data.content && typeof data.content === 'string') {\n                    // Extract first meaningful sentence or key information\n                    const content = data.content.substring(0, 200);\n                    if (content.length > 50) {\n                        findings.push(`From ${url}: ${content}...`);\n                    }\n                }\n                if (data.title) {\n                    findings.push(`Found: ${data.title} (${url})`);\n                }\n            }\n        }\n        // Add search result insights\n        if (memory.searchResults.length > 0) {\n            const topResult = memory.searchResults[0];\n            if (topResult && topResult.title) {\n                findings.push(`Top search result: ${topResult.title}`);\n            }\n        }\n        return findings.slice(0, 5); // Limit to top 5 findings\n    }\n    // Get memory for a task\n    getTaskMemory(taskId) {\n        return this.activeTasks.get(taskId);\n    }\n    // Smart snippet analysis to select best websites\n    async executeSnippetAnalysis(subtask, memory, config) {\n        if (memory.searchResults.length === 0) {\n            return {\n                selectedWebsites: [],\n                reasoning: 'No search results to analyze'\n            };\n        }\n        // Analyze snippets and select best websites\n        const analysisPrompt = `Analyze these search results and select the 3-5 most relevant websites based on their snippets and titles.\n\nTask: ${subtask.parameters?.originalTask || 'Unknown task'}\n\nSearch Results:\n${memory.searchResults.map((result, index)=>`${index + 1}. Title: ${result.title}\n     URL: ${result.url}\n     Snippet: ${result.snippet || result.description || 'No snippet available'}\n     Source: ${result.query} (${result.searchEngine})`).join('\\n\\n')}\n\nSelect the best websites that are most likely to contain the information needed. Consider:\n- Relevance to the task\n- Authority and reliability of the source\n- Freshness of content\n- Likelihood of containing specific data needed\n\nRespond with JSON:\n{\n  \"selectedUrls\": [\"url1\", \"url2\", \"url3\"],\n  \"reasoning\": \"explanation of why these sites were chosen\",\n  \"confidence\": 0.85\n}`;\n        try {\n            // This would call an AI service to analyze snippets\n            // For now, implement a simple heuristic-based selection\n            const selectedWebsites = this.selectWebsitesHeuristic(memory.searchResults, subtask, config);\n            memory.selectedWebsites = selectedWebsites;\n            return {\n                selectedWebsites,\n                reasoning: 'Selected based on relevance heuristics',\n                confidence: 0.75\n            };\n        } catch (error) {\n            console.error('Snippet analysis failed:', error);\n            // Fallback: select first few results\n            const fallbackSelection = memory.searchResults.slice(0, 3).map((r)=>r.url);\n            memory.selectedWebsites = fallbackSelection;\n            return {\n                selectedWebsites: fallbackSelection,\n                reasoning: 'Fallback selection due to analysis error',\n                confidence: 0.5\n            };\n        }\n    }\n    // Execute result analysis to select best websites\n    async executeResultAnalysis(subtask, memory, config) {\n        console.log('🔍 Analyzing search results to select best websites');\n        if (memory.searchResults.length === 0) {\n            throw new Error('No search results available for analysis');\n        }\n        // Analyze and select best websites based on search results\n        const selectedWebsites = this.selectWebsitesHeuristic(memory.searchResults, subtask, config);\n        memory.selectedWebsites = selectedWebsites;\n        console.log(`📊 Selected ${selectedWebsites.length} websites for detailed browsing`);\n        return {\n            selectedWebsites,\n            totalResults: memory.searchResults.length,\n            reasoning: 'Selected based on relevance, authority, and content quality indicators',\n            confidence: 0.8\n        };\n    }\n    // Heuristic-based website selection\n    selectWebsitesHeuristic(searchResults, subtask, config) {\n        const scored = searchResults.map((result)=>{\n            let score = 0;\n            const title = (result.title || '').toLowerCase();\n            const snippet = (result.snippet || result.description || '').toLowerCase();\n            const url = (result.url || '').toLowerCase();\n            // Boost official/authoritative sources\n            if (url.includes('.gov') || url.includes('.edu') || url.includes('.org')) score += 20;\n            // Boost well-known domains\n            const knownDomains = [\n                'wikipedia',\n                'amazon',\n                'apple',\n                'google',\n                'microsoft',\n                'github'\n            ];\n            if (knownDomains.some((domain)=>url.includes(domain))) score += 15;\n            // Boost if snippet contains relevant keywords\n            const taskKeywords = subtask.parameters?.keywords || [];\n            taskKeywords.forEach((keyword)=>{\n                if (snippet.includes(keyword.toLowerCase())) score += 10;\n                if (title.includes(keyword.toLowerCase())) score += 15;\n            });\n            // Penalize very long URLs (often less reliable)\n            if (url.length > 100) score -= 5;\n            // Boost if has good snippet (indicates content richness)\n            if (snippet.length > 100) score += 5;\n            return {\n                ...result,\n                score\n            };\n        });\n        // Sort by score and take top 3-5\n        return scored.sort((a, b)=>b.score - a.score).slice(0, Math.min(5, config.maxSites || 5)).map((r)=>r.url);\n    }\n    // Check if task completion criteria are met\n    async executeCompletionCheck(subtask, memory, config) {\n        const completionStatus = this.assessCompletionStatus(memory, subtask.parameters?.originalTask || '');\n        return {\n            status: completionStatus,\n            canComplete: completionStatus === 'sufficient' || completionStatus === 'complete',\n            dataPoints: Object.keys(memory.gatheredData).length,\n            reasoning: this.getCompletionReasoning(memory, completionStatus)\n        };\n    }\n    // Assess completion status based on gathered data\n    assessCompletionStatus(memory, originalTask) {\n        const dataPoints = Object.keys(memory.gatheredData).length;\n        const hasScreenshots = memory.screenshots.length > 0;\n        const visitedSites = memory.visitedUrls.length;\n        const successfulVisits = memory.visitedUrls.filter((url)=>memory.gatheredData[url]).length;\n        const searchQueries = memory.searchQueries.length;\n        console.log(`📊 Completion assessment: ${dataPoints} data points, ${successfulVisits}/${visitedSites} successful visits, ${searchQueries} searches`);\n        // Enhanced completion assessment with content quality\n        const contentQuality = this.assessContentQuality(memory);\n        if (dataPoints === 0 && searchQueries === 0) return 'insufficient';\n        if (dataPoints === 0 && searchQueries > 0) return 'insufficient';\n        if (successfulVisits === 0 && dataPoints < 2) return 'partial';\n        if (successfulVisits >= 1 && dataPoints >= 2 && contentQuality >= 0.5) return 'sufficient';\n        if (successfulVisits >= 3 && dataPoints >= 3 && contentQuality >= 0.7) return 'complete';\n        if (dataPoints >= 5 && hasScreenshots && visitedSites >= 3) return 'complete';\n        return 'partial';\n    }\n    // Assess the quality of gathered content\n    assessContentQuality(memory) {\n        let totalScore = 0;\n        let scoredItems = 0;\n        for (const [key, data] of Object.entries(memory.gatheredData)){\n            if (data && typeof data === 'object') {\n                let itemScore = 0;\n                // Check for content richness\n                if (data.content && typeof data.content === 'string') {\n                    if (data.content.length > 500) itemScore += 0.3;\n                    if (data.content.length > 1000) itemScore += 0.2;\n                }\n                // Check for structured data\n                if (data.title) itemScore += 0.1;\n                if (data.url) itemScore += 0.1;\n                if (data.timestamp) itemScore += 0.1;\n                // Check for successful extraction\n                if (data.success !== false) itemScore += 0.2;\n                totalScore += Math.min(itemScore, 1.0);\n                scoredItems++;\n            }\n        }\n        const quality = scoredItems > 0 ? totalScore / scoredItems : 0;\n        console.log(`📈 Content quality score: ${quality.toFixed(2)} (${scoredItems} items assessed)`);\n        return quality;\n    }\n    // Check if we have enough information to complete the task early\n    async checkTaskCompletion(memory, originalTask) {\n        const status = this.assessCompletionStatus(memory, originalTask);\n        const hasEnoughData = status === 'sufficient' || status === 'complete';\n        if (hasEnoughData) {\n            console.log(`🎯 Task completion criteria met: ${status}`);\n            // Additional intelligent checks\n            const intelligentCompletion = await this.performIntelligentCompletionCheck(memory, originalTask);\n            if (intelligentCompletion.canComplete) {\n                console.log(`🧠 Intelligent completion confirmed: ${intelligentCompletion.reasoning}`);\n                return true;\n            }\n        }\n        return false;\n    }\n    // Perform intelligent completion check using gathered data\n    async performIntelligentCompletionCheck(memory, originalTask) {\n        const dataPoints = Object.keys(memory.gatheredData).length;\n        const successfulVisits = memory.visitedUrls.filter((url)=>memory.gatheredData[url]).length;\n        const contentQuality = this.assessContentQuality(memory);\n        // Task-specific completion logic\n        const taskLower = originalTask.toLowerCase();\n        // For price/comparison tasks\n        if (taskLower.includes('price') || taskLower.includes('cost') || taskLower.includes('compare')) {\n            if (successfulVisits >= 2 && dataPoints >= 2) {\n                return {\n                    canComplete: true,\n                    reasoning: 'Found pricing information from multiple sources',\n                    confidence: 0.85\n                };\n            }\n        }\n        // For research/information tasks\n        if (taskLower.includes('research') || taskLower.includes('information') || taskLower.includes('about')) {\n            if (successfulVisits >= 3 && contentQuality >= 0.6) {\n                return {\n                    canComplete: true,\n                    reasoning: 'Gathered comprehensive information from multiple authoritative sources',\n                    confidence: 0.8\n                };\n            }\n        }\n        // For specific fact-finding tasks\n        if (taskLower.includes('when') || taskLower.includes('what') || taskLower.includes('where')) {\n            if (successfulVisits >= 1 && contentQuality >= 0.5) {\n                return {\n                    canComplete: true,\n                    reasoning: 'Found specific factual information',\n                    confidence: 0.75\n                };\n            }\n        }\n        // General completion criteria\n        if (successfulVisits >= 3 && dataPoints >= 3 && contentQuality >= 0.7) {\n            return {\n                canComplete: true,\n                reasoning: 'Sufficient high-quality data gathered from multiple sources',\n                confidence: 0.9\n            };\n        }\n        return {\n            canComplete: false,\n            reasoning: `Need more data: ${successfulVisits} visits, quality ${contentQuality.toFixed(2)}`,\n            confidence: 0.3\n        };\n    }\n    // Get reasoning for completion status\n    getCompletionReasoning(memory, status) {\n        const dataPoints = Object.keys(memory.gatheredData).length;\n        const visitedSites = memory.visitedUrls.length;\n        switch(status){\n            case 'insufficient':\n                return `Need more data. Currently have ${dataPoints} data points from ${visitedSites} sites.`;\n            case 'partial':\n                return `Making progress. Have ${dataPoints} data points from ${visitedSites} sites, but need more comprehensive data.`;\n            case 'sufficient':\n                return `Have enough data to complete task. Collected ${dataPoints} data points from ${visitedSites} sites.`;\n            case 'complete':\n                return `Task fully complete. Comprehensive data collected: ${dataPoints} data points, ${memory.screenshots.length} screenshots, ${visitedSites} sites visited.`;\n            default:\n                return 'Status assessment in progress.';\n        }\n    }\n    // Create new memory for a task\n    createTaskMemory(taskId) {\n        const memory = {\n            taskId,\n            completedSubtasks: [],\n            gatheredData: {},\n            visitedUrls: [],\n            searchQueries: [],\n            screenshots: [],\n            searchResults: [],\n            selectedWebsites: [],\n            completionStatus: 'insufficient',\n            lastUpdate: new Date().toISOString(),\n            isComplete: false\n        };\n        this.activeTasks.set(taskId, memory);\n        return memory;\n    }\n    /**\n   * Save browsing memory to persistent storage via Memory node\n   */ async saveMemoryToPersistentStorage(memory) {\n        if (!this.memoryNodeId || !this.workflowId || !this.userId) {\n            return;\n        }\n        try {\n            const browsingData = {\n                completedSubtasks: memory.completedSubtasks,\n                visitedUrls: memory.visitedUrls,\n                searchQueries: memory.searchQueries,\n                gatheredData: memory.gatheredData,\n                currentContext: memory.taskId,\n                preferences: {\n                    searchEngines: memory.searchResults.map((r)=>r.searchEngine).filter((v, i, a)=>a.indexOf(v) === i),\n                    successfulSites: memory.visitedUrls.filter((url)=>memory.gatheredData[url]),\n                    completionStatus: memory.completionStatus\n                }\n            };\n            const success = await _memory_MemoryService__WEBPACK_IMPORTED_MODULE_1__.memoryService.storeBrowsingMemory('browsing_memory', this.memoryNodeId, this.workflowId, this.userId, browsingData, {\n                memoryName: 'browsing_memory',\n                maxSize: 10240,\n                encryption: true\n            });\n            if (success) {\n                console.log('🧠 Browsing memory saved to persistent storage');\n            } else {\n                console.warn('⚠️ Failed to save browsing memory');\n            }\n        } catch (error) {\n            console.error('Error saving browsing memory:', error);\n        }\n    }\n    // Get service statistics\n    getStats() {\n        return {\n            activeTasks: this.activeTasks.size,\n            browserlessStats: this.browserless.getStats(),\n            memoryConnected: !!this.memoryNodeId,\n            tasks: Array.from(this.activeTasks.values()).map((memory)=>({\n                    taskId: memory.taskId,\n                    completedSubtasks: memory.completedSubtasks.length,\n                    isComplete: memory.isComplete,\n                    lastUpdate: memory.lastUpdate\n                }))\n        };\n    }\n    /**\n   * Create a detailed browsing plan using AI planner\n   */ async createDetailedPlanWithAI(task, aiProviderConfig, browsingConfig) {\n        const maxSubtasks = browsingConfig.maxSites || 5;\n        console.log(`📋 Creating AI-enhanced browsing plan for: ${task}`);\n        try {\n            // Create intelligent plan based on task type\n            const plan = {\n                id: `ai_plan_${Date.now()}`,\n                task,\n                subtasks: [\n                    {\n                        id: 'search_1',\n                        type: 'search',\n                        description: `Primary search for: ${task}`,\n                        target: task,\n                        status: 'pending',\n                        parameters: {\n                            extractionGoal: 'Find relevant websites and initial information'\n                        }\n                    },\n                    {\n                        id: 'search_2',\n                        type: 'search',\n                        description: `Secondary search for detailed information`,\n                        target: `${task} detailed information`,\n                        status: 'pending',\n                        parameters: {\n                            extractionGoal: 'Find additional sources and specific details'\n                        }\n                    },\n                    {\n                        id: 'analyze_1',\n                        type: 'analyze_results',\n                        description: 'Analyze search results and select best websites',\n                        target: 'search_results',\n                        status: 'pending',\n                        parameters: {\n                            extractionGoal: 'Select top 3 most relevant websites based on snippets'\n                        }\n                    },\n                    {\n                        id: 'navigate_1',\n                        type: 'navigate',\n                        description: 'Visit selected websites and extract information',\n                        target: 'selected_websites',\n                        status: 'pending',\n                        parameters: {\n                            extractionGoal: 'Extract specific information related to the task'\n                        }\n                    },\n                    {\n                        id: 'completion_check',\n                        type: 'check_completion',\n                        description: 'Check if sufficient information has been gathered',\n                        target: 'gathered_data',\n                        status: 'pending',\n                        parameters: {\n                            extractionGoal: 'Determine if task is complete or needs more information'\n                        }\n                    }\n                ],\n                estimatedTime: Math.min(maxSubtasks * 2, 10),\n                priority: 'medium'\n            };\n            console.log(`📋 Created AI-enhanced plan with ${plan.subtasks.length} subtasks`);\n            return plan;\n        } catch (error) {\n            console.error('Failed to create AI plan, using fallback:', error);\n            // Fallback to simple plan\n            return {\n                id: `fallback_plan_${Date.now()}`,\n                task,\n                subtasks: [\n                    {\n                        id: 'search_fallback',\n                        type: 'search',\n                        description: `Search for: ${task}`,\n                        target: task,\n                        status: 'pending'\n                    }\n                ],\n                estimatedTime: 5,\n                priority: 'medium'\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/intelligentBrowsing.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/memory/MemoryService.ts":
/*!*****************************************!*\
  !*** ./src/lib/memory/MemoryService.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MemoryService: () => (/* binding */ MemoryService),\n/* harmony export */   memoryService: () => (/* binding */ memoryService)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/**\n * Memory Service for Manual Build Workflows\n * Provides persistent memory storage and retrieval for connected nodes\n */ \nconst supabaseUrl = \"https://hpkzzhpufhbxtxqaugjh.supabase.co\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nclass MemoryService {\n    constructor(){\n        this.memoryCache = new Map();\n        this.cacheExpiry = new Map();\n        this.CACHE_TTL = 5 * 60 * 1000 // 5 minutes\n        ;\n        this.supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey);\n    }\n    /**\n   * Store data in memory for a specific node\n   */ async store(memoryName, nodeId, workflowId, userId, data, dataType = 'general', config) {\n        try {\n            const dataString = JSON.stringify(data);\n            const sizeKB = new TextEncoder().encode(dataString).length / 1024;\n            // Check size limit\n            if (sizeKB > config.maxSize) {\n                console.warn(`Memory data exceeds size limit: ${sizeKB}KB > ${config.maxSize}KB`);\n                return false;\n            }\n            // Encrypt if enabled\n            let finalData = data;\n            if (config.encryption) {\n                finalData = this.encrypt(dataString);\n            }\n            const memoryEntry = {\n                memory_name: memoryName,\n                user_id: userId,\n                workflow_id: workflowId,\n                node_id: nodeId,\n                data_type: dataType,\n                data: finalData,\n                metadata: {\n                    created_at: new Date().toISOString(),\n                    updated_at: new Date().toISOString(),\n                    size_kb: sizeKB,\n                    encrypted: config.encryption\n                }\n            };\n            // Upsert to database\n            const { error } = await this.supabase.from('workflow_memory').upsert(memoryEntry, {\n                onConflict: 'memory_name,user_id,workflow_id,node_id'\n            });\n            if (error) {\n                console.error('Memory store error:', error);\n                return false;\n            }\n            // Update cache\n            const cacheKey = `${userId}:${workflowId}:${nodeId}:${memoryName}`;\n            this.memoryCache.set(cacheKey, data);\n            this.cacheExpiry.set(cacheKey, Date.now() + this.CACHE_TTL);\n            console.log(`✅ Memory stored: ${memoryName} (${sizeKB.toFixed(2)}KB)`);\n            return true;\n        } catch (error) {\n            console.error('Memory store error:', error);\n            return false;\n        }\n    }\n    /**\n   * Retrieve data from memory for a specific node\n   */ async retrieve(memoryName, nodeId, workflowId, userId) {\n        try {\n            const cacheKey = `${userId}:${workflowId}:${nodeId}:${memoryName}`;\n            // Check cache first\n            if (this.memoryCache.has(cacheKey)) {\n                const expiry = this.cacheExpiry.get(cacheKey) || 0;\n                if (Date.now() < expiry) {\n                    console.log(`🚀 Memory cache hit: ${memoryName}`);\n                    return this.memoryCache.get(cacheKey);\n                } else {\n                    // Expired\n                    this.memoryCache.delete(cacheKey);\n                    this.cacheExpiry.delete(cacheKey);\n                }\n            }\n            // Fetch from database\n            const { data, error } = await this.supabase.from('workflow_memory').select('*').eq('memory_name', memoryName).eq('user_id', userId).eq('workflow_id', workflowId).eq('node_id', nodeId).single();\n            if (error || !data) {\n                console.log(`📭 Memory not found: ${memoryName}`);\n                return null;\n            }\n            // Decrypt if needed\n            let finalData = data.data;\n            if (data.metadata.encrypted) {\n                finalData = this.decrypt(data.data);\n            }\n            // Update cache\n            this.memoryCache.set(cacheKey, finalData);\n            this.cacheExpiry.set(cacheKey, Date.now() + this.CACHE_TTL);\n            console.log(`📖 Memory retrieved: ${memoryName} (${data.metadata.size_kb.toFixed(2)}KB)`);\n            return finalData;\n        } catch (error) {\n            console.error('Memory retrieve error:', error);\n            return null;\n        }\n    }\n    /**\n   * Store browsing memory (specialized for browsing nodes)\n   */ async storeBrowsingMemory(memoryName, nodeId, workflowId, userId, browsingData, config) {\n        return this.store(memoryName, nodeId, workflowId, userId, browsingData, 'browsing', config);\n    }\n    /**\n   * Store routing memory (specialized for router nodes)\n   */ async storeRoutingMemory(memoryName, nodeId, workflowId, userId, routingData, config) {\n        return this.store(memoryName, nodeId, workflowId, userId, routingData, 'routing', config);\n    }\n    /**\n   * Get all memory entries for a workflow\n   */ async getWorkflowMemory(workflowId, userId) {\n        try {\n            const { data, error } = await this.supabase.from('workflow_memory').select('*').eq('workflow_id', workflowId).eq('user_id', userId);\n            if (error) {\n                console.error('Get workflow memory error:', error);\n                return [];\n            }\n            return data || [];\n        } catch (error) {\n            console.error('Get workflow memory error:', error);\n            return [];\n        }\n    }\n    /**\n   * Clear memory for a specific node\n   */ async clearNodeMemory(nodeId, workflowId, userId) {\n        try {\n            const { error } = await this.supabase.from('workflow_memory').delete().eq('node_id', nodeId).eq('workflow_id', workflowId).eq('user_id', userId);\n            if (error) {\n                console.error('Clear node memory error:', error);\n                return false;\n            }\n            // Clear cache\n            const cacheKeys = Array.from(this.memoryCache.keys()).filter((key)=>key.includes(`${userId}:${workflowId}:${nodeId}`));\n            cacheKeys.forEach((key)=>{\n                this.memoryCache.delete(key);\n                this.cacheExpiry.delete(key);\n            });\n            console.log(`🗑️ Memory cleared for node: ${nodeId}`);\n            return true;\n        } catch (error) {\n            console.error('Clear node memory error:', error);\n            return false;\n        }\n    }\n    /**\n   * Simple encryption (for demo - use proper encryption in production)\n   */ encrypt(data) {\n        // Simple base64 encoding for demo - replace with proper encryption\n        return Buffer.from(data).toString('base64');\n    }\n    /**\n   * Simple decryption (for demo - use proper decryption in production)\n   */ decrypt(encryptedData) {\n        try {\n            const decrypted = Buffer.from(encryptedData, 'base64').toString();\n            return JSON.parse(decrypted);\n        } catch (error) {\n            console.error('Decryption error:', error);\n            return null;\n        }\n    }\n    /**\n   * Get memory statistics\n   */ getStats() {\n        return {\n            cacheSize: this.memoryCache.size,\n            cacheHitRate: '95%',\n            totalMemoryEntries: 'N/A' // Would need to query DB\n        };\n    }\n}\n// Export singleton instance\nconst memoryService = new MemoryService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/memory/MemoryService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/memory/RouterMemoryService.ts":
/*!***********************************************!*\
  !*** ./src/lib/memory/RouterMemoryService.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RouterMemoryService: () => (/* binding */ RouterMemoryService),\n/* harmony export */   routerMemoryService: () => (/* binding */ routerMemoryService)\n/* harmony export */ });\n/* harmony import */ var _MemoryService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./MemoryService */ \"(rsc)/./src/lib/memory/MemoryService.ts\");\n/**\n * Router Memory Service\n * Handles memory for Central Router nodes - learning from routing decisions\n */ \nclass RouterMemoryService {\n    /**\n   * Connect to a Memory node for persistent storage\n   */ connectMemory(memoryNodeId, workflowId, userId) {\n        this.memoryNodeId = memoryNodeId;\n        this.workflowId = workflowId;\n        this.userId = userId;\n        console.log(`🧠 Router connected to Memory node: ${memoryNodeId}`);\n        // Load existing memory\n        this.loadMemoryFromStorage();\n    }\n    /**\n   * Record a routing decision for learning\n   */ async recordRoutingDecision(query, selectedProvider, reason, responseTime, success) {\n        const decision = {\n            query,\n            selectedProvider,\n            reason,\n            performance: success ? Math.max(0.1, 1 - responseTime / 10000) : 0,\n            timestamp: new Date().toISOString(),\n            responseTime,\n            success\n        };\n        this.routingHistory.push(decision);\n        // Update provider performance\n        this.updateProviderPerformance(selectedProvider, decision);\n        // Save to persistent memory\n        await this.saveMemoryToStorage();\n        console.log(`📊 Recorded routing decision: ${selectedProvider} for \"${query.substring(0, 50)}...\"`);\n    }\n    /**\n   * Get smart routing recommendation based on memory\n   */ getRoutingRecommendation(query, availableProviders, taskType) {\n        // Check user preferences first\n        if (taskType && this.userPreferences.taskTypePreferences[taskType]) {\n            const preferredProvider = this.userPreferences.taskTypePreferences[taskType];\n            if (availableProviders.includes(preferredProvider)) {\n                return {\n                    recommendedProvider: preferredProvider,\n                    confidence: 0.9,\n                    reason: `User prefers ${preferredProvider} for ${taskType} tasks`\n                };\n            }\n        }\n        // Check for similar queries in history\n        const similarDecisions = this.findSimilarQueries(query, 5);\n        if (similarDecisions.length > 0) {\n            const successfulDecisions = similarDecisions.filter((d)=>d.success && d.performance > 0.6);\n            if (successfulDecisions.length > 0) {\n                const bestDecision = successfulDecisions.reduce((best, current)=>current.performance > best.performance ? current : best);\n                if (availableProviders.includes(bestDecision.selectedProvider)) {\n                    return {\n                        recommendedProvider: bestDecision.selectedProvider,\n                        confidence: 0.8,\n                        reason: `${bestDecision.selectedProvider} performed well on similar queries`\n                    };\n                }\n            }\n        }\n        // Fall back to provider performance\n        const performanceScores = availableProviders.map((providerId)=>{\n            const perf = this.providerPerformance.get(providerId);\n            if (!perf) {\n                return {\n                    providerId,\n                    score: 0.5\n                }; // Neutral score for unknown providers\n            }\n            // Calculate composite score based on success rate, speed, and user satisfaction\n            const score = perf.successRate * 0.4 + (1 - Math.min(perf.averageResponseTime / 5000, 1)) * 0.3 + perf.userSatisfaction * 0.3;\n            return {\n                providerId,\n                score\n            };\n        });\n        const bestProvider = performanceScores.reduce((best, current)=>current.score > best.score ? current : best);\n        return {\n            recommendedProvider: bestProvider.providerId,\n            confidence: Math.min(bestProvider.score, 0.7),\n            reason: `${bestProvider.providerId} has the best overall performance (${(bestProvider.score * 100).toFixed(1)}%)`\n        };\n    }\n    /**\n   * Update user preferences based on feedback\n   */ async updateUserPreferences(providerId, feedback, taskType) {\n        if (feedback === 'positive') {\n            if (!this.userPreferences.preferredProviders.includes(providerId)) {\n                this.userPreferences.preferredProviders.push(providerId);\n            }\n            if (taskType) {\n                this.userPreferences.taskTypePreferences[taskType] = providerId;\n            }\n            // Remove from avoided if present\n            this.userPreferences.avoidedProviders = this.userPreferences.avoidedProviders.filter((p)=>p !== providerId);\n        } else if (feedback === 'negative') {\n            if (!this.userPreferences.avoidedProviders.includes(providerId)) {\n                this.userPreferences.avoidedProviders.push(providerId);\n            }\n            // Remove from preferred if present\n            this.userPreferences.preferredProviders = this.userPreferences.preferredProviders.filter((p)=>p !== providerId);\n            if (taskType && this.userPreferences.taskTypePreferences[taskType] === providerId) {\n                delete this.userPreferences.taskTypePreferences[taskType];\n            }\n        }\n        // Update the latest routing decision with feedback\n        const latestDecision = this.routingHistory[this.routingHistory.length - 1];\n        if (latestDecision && latestDecision.selectedProvider === providerId) {\n            latestDecision.userFeedback = feedback;\n        }\n        await this.saveMemoryToStorage();\n        console.log(`👍 Updated user preferences: ${feedback} feedback for ${providerId}`);\n    }\n    /**\n   * Get routing statistics and insights\n   */ getRoutingStats() {\n        const totalDecisions = this.routingHistory.length;\n        const successfulDecisions = this.routingHistory.filter((d)=>d.success).length;\n        const averageResponseTime = this.routingHistory.reduce((sum, d)=>sum + d.responseTime, 0) / totalDecisions || 0;\n        const providerUsage = this.routingHistory.reduce((acc, decision)=>{\n            acc[decision.selectedProvider] = (acc[decision.selectedProvider] || 0) + 1;\n            return acc;\n        }, {});\n        return {\n            totalDecisions,\n            successRate: totalDecisions > 0 ? successfulDecisions / totalDecisions : 0,\n            averageResponseTime: Math.round(averageResponseTime),\n            providerUsage,\n            userPreferences: this.userPreferences,\n            memoryConnected: !!this.memoryNodeId,\n            lastDecision: this.routingHistory[this.routingHistory.length - 1]?.timestamp\n        };\n    }\n    /**\n   * Private helper methods\n   */ updateProviderPerformance(providerId, decision) {\n        let perf = this.providerPerformance.get(providerId);\n        if (!perf) {\n            perf = {\n                providerId,\n                averageResponseTime: decision.responseTime,\n                successRate: decision.success ? 1 : 0,\n                userSatisfaction: 0.5,\n                totalRequests: 1,\n                lastUsed: decision.timestamp\n            };\n        } else {\n            // Update running averages\n            perf.averageResponseTime = (perf.averageResponseTime * perf.totalRequests + decision.responseTime) / (perf.totalRequests + 1);\n            perf.successRate = (perf.successRate * perf.totalRequests + (decision.success ? 1 : 0)) / (perf.totalRequests + 1);\n            perf.totalRequests += 1;\n            perf.lastUsed = decision.timestamp;\n            // Update user satisfaction based on feedback\n            if (decision.userFeedback) {\n                const feedbackScore = decision.userFeedback === 'positive' ? 1 : decision.userFeedback === 'negative' ? 0 : 0.5;\n                perf.userSatisfaction = perf.userSatisfaction * 0.8 + feedbackScore * 0.2; // Weighted average\n            }\n        }\n        this.providerPerformance.set(providerId, perf);\n    }\n    findSimilarQueries(query, limit = 5) {\n        const queryWords = query.toLowerCase().split(/\\s+/);\n        return this.routingHistory.map((decision)=>{\n            const decisionWords = decision.query.toLowerCase().split(/\\s+/);\n            const commonWords = queryWords.filter((word)=>decisionWords.includes(word));\n            const similarity = commonWords.length / Math.max(queryWords.length, decisionWords.length);\n            return {\n                decision,\n                similarity\n            };\n        }).filter((item)=>item.similarity > 0.3) // At least 30% similarity\n        .sort((a, b)=>b.similarity - a.similarity).slice(0, limit).map((item)=>item.decision);\n    }\n    async loadMemoryFromStorage() {\n        if (!this.memoryNodeId || !this.workflowId || !this.userId) {\n            return;\n        }\n        try {\n            const routingData = await _MemoryService__WEBPACK_IMPORTED_MODULE_0__.memoryService.retrieve('routing_memory', this.memoryNodeId, this.workflowId, this.userId);\n            if (routingData) {\n                this.routingHistory = routingData.routingDecisions || [];\n                this.userPreferences = {\n                    ...this.userPreferences,\n                    ...routingData.userPreferences\n                };\n                // Rebuild provider performance map from stored scores\n                if (routingData.providerPerformance) {\n                    Object.entries(routingData.providerPerformance).forEach(([providerId, score])=>{\n                        // Create a basic ProviderPerformance object from the stored score\n                        // This is a simplified reconstruction - detailed stats will be rebuilt over time\n                        const perf = {\n                            providerId,\n                            averageResponseTime: 2000,\n                            successRate: typeof score === 'number' ? Math.max(0.1, score) : 0.5,\n                            userSatisfaction: typeof score === 'number' ? score : 0.5,\n                            totalRequests: 1,\n                            lastUsed: new Date().toISOString()\n                        };\n                        this.providerPerformance.set(providerId, perf);\n                    });\n                }\n                console.log(`🧠 Loaded routing memory: ${this.routingHistory.length} decisions`);\n            }\n        } catch (error) {\n            console.error('Error loading routing memory:', error);\n        }\n    }\n    async saveMemoryToStorage() {\n        if (!this.memoryNodeId || !this.workflowId || !this.userId) {\n            return;\n        }\n        try {\n            const routingData = {\n                routingDecisions: this.routingHistory.slice(-100),\n                userPreferences: this.userPreferences,\n                providerPerformance: Object.fromEntries(Array.from(this.providerPerformance.entries()).map(([providerId, perf])=>[\n                        providerId,\n                        // Calculate composite performance score (0-1)\n                        perf.successRate * 0.4 + (1 - Math.min(perf.averageResponseTime / 5000, 1)) * 0.3 + perf.userSatisfaction * 0.3\n                    ])),\n                learningData: {\n                    totalDecisions: this.routingHistory.length,\n                    lastUpdate: new Date().toISOString()\n                }\n            };\n            const success = await _MemoryService__WEBPACK_IMPORTED_MODULE_0__.memoryService.storeRoutingMemory('routing_memory', this.memoryNodeId, this.workflowId, this.userId, routingData, {\n                memoryName: 'routing_memory',\n                maxSize: 5120,\n                encryption: true\n            });\n            if (success) {\n                console.log('🧠 Routing memory saved to persistent storage');\n            }\n        } catch (error) {\n            console.error('Error saving routing memory:', error);\n        }\n    }\n    constructor(){\n        this.memoryNodeId = null;\n        this.workflowId = null;\n        this.userId = null;\n        this.routingHistory = [];\n        this.providerPerformance = new Map();\n        this.userPreferences = {\n            preferredProviders: [],\n            avoidedProviders: [],\n            taskTypePreferences: {},\n            qualityThreshold: 0.7,\n            speedPreference: 'balanced'\n        };\n    }\n}\n// Export singleton instance\nconst routerMemoryService = new RouterMemoryService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/memory/RouterMemoryService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient),\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n// Service role client for admin operations (OAuth token storage, etc.)\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/websocket/WorkflowWebSocketServer.ts":
/*!******************************************************!*\
  !*** ./src/lib/websocket/WorkflowWebSocketServer.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   broadcastToConnection: () => (/* binding */ broadcastToConnection),\n/* harmony export */   broadcastToUser: () => (/* binding */ broadcastToUser),\n/* harmony export */   broadcastToWorkflow: () => (/* binding */ broadcastToWorkflow),\n/* harmony export */   cleanupInactiveConnections: () => (/* binding */ cleanupInactiveConnections),\n/* harmony export */   createWorkflowEventStream: () => (/* binding */ createWorkflowEventStream),\n/* harmony export */   emitWorkflowEvent: () => (/* binding */ emitWorkflowEvent),\n/* harmony export */   getActiveConnections: () => (/* binding */ getActiveConnections),\n/* harmony export */   getWorkflowConnectionsCount: () => (/* binding */ getWorkflowConnectionsCount),\n/* harmony export */   registerWorkflowConnection: () => (/* binding */ registerWorkflowConnection),\n/* harmony export */   unregisterWorkflowConnection: () => (/* binding */ unregisterWorkflowConnection)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/**\n * WebSocket Server for Real-time Workflow Updates\n * Provides live updates during Manual Build workflow execution\n */ \n// In-memory storage for active WebSocket connections\nconst activeConnections = new Map();\n/**\n * Register a new WebSocket connection for workflow updates\n */ function registerWorkflowConnection(connectionId, workflowId, userId, controller) {\n    const connection = {\n        workflowId,\n        userId,\n        controller,\n        connectedAt: new Date().toISOString()\n    };\n    activeConnections.set(connectionId, connection);\n    console.log(`[Workflow WebSocket] Registered connection ${connectionId} for workflow ${workflowId}`);\n    // Send initial connection event\n    const connectionEvent = {\n        id: crypto.randomUUID(),\n        workflowId,\n        type: 'connection_established',\n        timestamp: new Date().toISOString(),\n        data: {\n            message: '🔗 Connected to workflow real-time updates',\n            workflowId,\n            connectionId\n        },\n        userId\n    };\n    broadcastToConnection(connectionId, connectionEvent);\n}\n/**\n * Unregister a WebSocket connection\n */ function unregisterWorkflowConnection(connectionId) {\n    const connection = activeConnections.get(connectionId);\n    if (connection) {\n        activeConnections.delete(connectionId);\n        console.log(`[Workflow WebSocket] Unregistered connection ${connectionId} for workflow ${connection.workflowId}`);\n    }\n}\n/**\n * Broadcast event to a specific connection\n */ function broadcastToConnection(connectionId, event) {\n    const connection = activeConnections.get(connectionId);\n    if (connection) {\n        try {\n            const encoder = new TextEncoder();\n            const eventData = `id: ${event.id}\\nevent: ${event.type}\\ndata: ${JSON.stringify(event)}\\n\\n`;\n            connection.controller.enqueue(encoder.encode(eventData));\n            connection.lastEventId = event.id;\n            console.log(`[Workflow WebSocket] Sent ${event.type} to connection ${connectionId}`);\n        } catch (error) {\n            console.error(`[Workflow WebSocket] Error sending to connection ${connectionId}:`, error);\n            // Remove dead connection\n            activeConnections.delete(connectionId);\n        }\n    }\n}\n/**\n * Broadcast event to all connections for a specific workflow\n */ function broadcastToWorkflow(workflowId, event) {\n    const workflowConnections = Array.from(activeConnections.entries()).filter(([_, connection])=>connection.workflowId === workflowId);\n    workflowConnections.forEach(([connectionId, _])=>{\n        broadcastToConnection(connectionId, event);\n    });\n    console.log(`[Workflow WebSocket] Broadcasted ${event.type} to ${workflowConnections.length} connections for workflow ${workflowId}`);\n}\n/**\n * Broadcast event to all connections for a specific user\n */ function broadcastToUser(userId, event) {\n    const userConnections = Array.from(activeConnections.entries()).filter(([_, connection])=>connection.userId === userId);\n    userConnections.forEach(([connectionId, _])=>{\n        broadcastToConnection(connectionId, event);\n    });\n    console.log(`[Workflow WebSocket] Broadcasted ${event.type} to ${userConnections.length} connections for user ${userId}`);\n}\n/**\n * Get active connections count for a workflow\n */ function getWorkflowConnectionsCount(workflowId) {\n    return Array.from(activeConnections.values()).filter((connection)=>connection.workflowId === workflowId).length;\n}\n/**\n * Get all active connections for monitoring\n */ function getActiveConnections() {\n    return new Map(activeConnections);\n}\n/**\n * Emit workflow event to all relevant connections\n */ function emitWorkflowEvent(workflowId, userId, eventType, data, executionId) {\n    const event = {\n        id: crypto.randomUUID(),\n        workflowId,\n        executionId,\n        type: eventType,\n        timestamp: new Date().toISOString(),\n        data,\n        userId\n    };\n    // Broadcast to all connections for this workflow\n    broadcastToWorkflow(workflowId, event);\n    // Store event in database for persistence (optional)\n    storeWorkflowEvent(event).catch((error)=>{\n        console.error('[Workflow WebSocket] Failed to store event:', error);\n    });\n}\n/**\n * Store workflow event in database for persistence and replay\n */ async function storeWorkflowEvent(event) {\n    try {\n        const supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n        await supabase.from('workflow_execution_logs').insert({\n            execution_id: event.executionId || event.workflowId,\n            workflow_id: event.workflowId,\n            node_id: event.data?.nodeId || 'system',\n            node_type: event.data?.nodeType || 'system',\n            log_level: event.type.includes('failed') ? 'error' : 'info',\n            message: event.data?.message || `Workflow event: ${event.type}`,\n            data: event.data,\n            duration_ms: event.data?.duration,\n            created_at: event.timestamp\n        });\n        console.log(`[Workflow WebSocket] Stored event ${event.type} for workflow ${event.workflowId}`);\n    } catch (error) {\n        console.error('[Workflow WebSocket] Failed to store event in database:', error);\n    }\n}\n/**\n * Create a Server-Sent Events stream for workflow updates\n */ function createWorkflowEventStream(workflowId, userId) {\n    const connectionId = crypto.randomUUID();\n    const encoder = new TextEncoder();\n    return new ReadableStream({\n        start (controller) {\n            registerWorkflowConnection(connectionId, workflowId, userId, controller);\n        },\n        cancel () {\n            unregisterWorkflowConnection(connectionId);\n            console.log(`[Workflow WebSocket] Stream cancelled for workflow ${workflowId}`);\n        }\n    });\n}\n/**\n * Cleanup inactive connections (called periodically)\n */ function cleanupInactiveConnections() {\n    const now = Date.now();\n    const maxAge = 30 * 60 * 1000; // 30 minutes\n    for (const [connectionId, connection] of activeConnections.entries()){\n        const connectionAge = now - new Date(connection.connectedAt).getTime();\n        if (connectionAge > maxAge) {\n            console.log(`[Workflow WebSocket] Cleaning up inactive connection ${connectionId}`);\n            unregisterWorkflowConnection(connectionId);\n        }\n    }\n}\n// Cleanup inactive connections every 5 minutes\nsetInterval(cleanupInactiveConnections, 5 * 60 * 1000);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/websocket/WorkflowWebSocketServer.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/workflow/ErrorRecoveryService.ts":
/*!**************************************************!*\
  !*** ./src/lib/workflow/ErrorRecoveryService.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorRecoveryService: () => (/* binding */ ErrorRecoveryService),\n/* harmony export */   errorRecoveryService: () => (/* binding */ errorRecoveryService)\n/* harmony export */ });\n/* harmony import */ var _lib_websocket_WorkflowWebSocketServer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/websocket/WorkflowWebSocketServer */ \"(rsc)/./src/lib/websocket/WorkflowWebSocketServer.ts\");\n/**\n * Advanced Error Recovery Service for Manual Build Workflows\n * Provides intelligent error handling, retry mechanisms, and recovery strategies\n */ \nclass ErrorRecoveryService {\n    constructor(){\n        this.recoveryStrategies = new Map();\n        this.errorHistory = new Map();\n        this.initializeDefaultStrategies();\n    }\n    /**\n   * Initialize default recovery strategies for different node types\n   */ initializeDefaultStrategies() {\n        // Provider node strategies\n        this.addRecoveryStrategy('provider', {\n            type: 'fallback',\n            description: 'Switch to fallback AI provider',\n            priority: 1,\n            condition: (context)=>context.attempt <= 2,\n            action: async (context)=>{\n                // Try fallback provider if configured\n                const node = context;\n                if (node.config?.fallbackProvider) {\n                    console.log(`🔄 Switching to fallback provider: ${node.config.fallbackProvider.providerId}`);\n                    return await this.executeWithFallbackProvider(context);\n                }\n                throw new Error('No fallback provider configured');\n            }\n        });\n        this.addRecoveryStrategy('provider', {\n            type: 'retry',\n            description: 'Retry with exponential backoff',\n            priority: 2,\n            condition: (context)=>context.attempt <= context.maxRetries,\n            action: async (context)=>{\n                const delay = Math.min(1000 * Math.pow(2, context.attempt - 1), 10000);\n                console.log(`⏳ Retrying in ${delay}ms (attempt ${context.attempt}/${context.maxRetries})`);\n                await new Promise((resolve)=>setTimeout(resolve, delay));\n                return await this.retryOriginalOperation(context);\n            }\n        });\n        // Browsing node strategies\n        this.addRecoveryStrategy('browsing', {\n            type: 'retry',\n            description: 'Retry browsing with different strategy',\n            priority: 1,\n            condition: (context)=>context.attempt <= 3,\n            action: async (context)=>{\n                console.log(`🌐 Retrying browsing operation with alternative approach`);\n                return await this.retryBrowsingWithFallback(context);\n            }\n        });\n        // Router node strategies\n        this.addRecoveryStrategy('centralRouter', {\n            type: 'alternative_path',\n            description: 'Route to alternative AI provider',\n            priority: 1,\n            condition: (context)=>this.hasAlternativeProviders(context),\n            action: async (context)=>{\n                console.log(`🔀 Routing to alternative provider`);\n                return await this.routeToAlternativeProvider(context);\n            }\n        });\n        // Memory node strategies\n        this.addRecoveryStrategy('memory', {\n            type: 'skip',\n            description: 'Continue without memory context',\n            priority: 1,\n            condition: ()=>true,\n            action: async (context)=>{\n                console.log(`🧠 Continuing without memory context`);\n                return {\n                    skipMemory: true,\n                    result: context.input\n                };\n            }\n        });\n        // Generic strategies for all nodes\n        this.addGenericRecoveryStrategies();\n    }\n    /**\n   * Add generic recovery strategies that apply to all node types\n   */ addGenericRecoveryStrategies() {\n        const genericStrategies = [\n            {\n                type: 'manual',\n                description: 'Request manual intervention',\n                priority: 10,\n                condition: (context)=>context.attempt > context.maxRetries,\n                action: async (context)=>{\n                    console.log(`👤 Requesting manual intervention for ${context.nodeType} node`);\n                    await this.requestManualIntervention(context);\n                    throw new Error('Manual intervention required');\n                }\n            }\n        ];\n        // Add generic strategies to all node types\n        const nodeTypes = [\n            'provider',\n            'browsing',\n            'centralRouter',\n            'memory',\n            'planner',\n            'classifier',\n            'tool'\n        ];\n        nodeTypes.forEach((nodeType)=>{\n            genericStrategies.forEach((strategy)=>{\n                this.addRecoveryStrategy(nodeType, strategy);\n            });\n        });\n    }\n    /**\n   * Add a recovery strategy for a specific node type\n   */ addRecoveryStrategy(nodeType, strategy) {\n        if (!this.recoveryStrategies.has(nodeType)) {\n            this.recoveryStrategies.set(nodeType, []);\n        }\n        this.recoveryStrategies.get(nodeType).push(strategy);\n        // Sort by priority (lower number = higher priority)\n        this.recoveryStrategies.get(nodeType).sort((a, b)=>a.priority - b.priority);\n    }\n    /**\n   * Attempt to recover from an error using available strategies\n   */ async recoverFromError(context) {\n        console.log(`🚨 Error recovery initiated for ${context.nodeType} node: ${context.error.message}`);\n        // Store error in history\n        this.addErrorToHistory(context);\n        // Emit error event\n        (0,_lib_websocket_WorkflowWebSocketServer__WEBPACK_IMPORTED_MODULE_0__.emitWorkflowEvent)(context.workflowId, context.userId, 'node_failed', {\n            nodeId: context.nodeId,\n            nodeType: context.nodeType,\n            nodeLabel: context.nodeLabel,\n            error: context.error.message,\n            attempt: context.attempt,\n            maxRetries: context.maxRetries\n        }, context.executionId);\n        // Get available strategies for this node type\n        const strategies = this.recoveryStrategies.get(context.nodeType) || [];\n        for (const strategy of strategies){\n            if (!strategy.condition || strategy.condition(context)) {\n                try {\n                    console.log(`🔧 Attempting recovery strategy: ${strategy.description}`);\n                    const result = await strategy.action(context);\n                    // Emit recovery success event\n                    (0,_lib_websocket_WorkflowWebSocketServer__WEBPACK_IMPORTED_MODULE_0__.emitWorkflowEvent)(context.workflowId, context.userId, 'log_message', {\n                        level: 'success',\n                        message: `Recovery successful: ${strategy.description}`,\n                        nodeId: context.nodeId,\n                        strategy: strategy.type\n                    }, context.executionId);\n                    return {\n                        success: true,\n                        result,\n                        strategy,\n                        message: `Recovery successful using strategy: ${strategy.description}`,\n                        shouldContinue: true,\n                        newNodeId: strategy.type === 'alternative_path' ? result.newNodeId : undefined\n                    };\n                } catch (recoveryError) {\n                    console.log(`❌ Recovery strategy failed: ${strategy.description}`, recoveryError);\n                    continue;\n                }\n            }\n        }\n        // All recovery strategies failed\n        (0,_lib_websocket_WorkflowWebSocketServer__WEBPACK_IMPORTED_MODULE_0__.emitWorkflowEvent)(context.workflowId, context.userId, 'workflow_failed', {\n            error: 'All recovery strategies exhausted',\n            nodeId: context.nodeId,\n            originalError: context.error.message\n        }, context.executionId);\n        return {\n            success: false,\n            message: `All recovery strategies exhausted for ${context.nodeType} node`,\n            shouldContinue: false\n        };\n    }\n    /**\n   * Add error to history for pattern analysis\n   */ addErrorToHistory(context) {\n        const key = `${context.workflowId}-${context.nodeId}`;\n        if (!this.errorHistory.has(key)) {\n            this.errorHistory.set(key, []);\n        }\n        this.errorHistory.get(key).push(context);\n        // Keep only last 10 errors per node\n        const history = this.errorHistory.get(key);\n        if (history.length > 10) {\n            history.splice(0, history.length - 10);\n        }\n    }\n    /**\n   * Execute with fallback provider\n   */ async executeWithFallbackProvider(context) {\n        // Implementation would depend on the specific provider execution logic\n        // This is a placeholder for the actual fallback execution\n        return {\n            fallbackUsed: true,\n            result: 'Fallback provider response'\n        };\n    }\n    /**\n   * Retry original operation\n   */ async retryOriginalOperation(context) {\n        // Implementation would retry the original operation\n        // This is a placeholder for the actual retry logic\n        return {\n            retried: true,\n            result: 'Retry successful'\n        };\n    }\n    /**\n   * Retry browsing with fallback strategy\n   */ async retryBrowsingWithFallback(context) {\n        // Implementation would use alternative browsing approach\n        return {\n            browsingFallback: true,\n            result: 'Alternative browsing successful'\n        };\n    }\n    /**\n   * Check if alternative providers are available\n   */ hasAlternativeProviders(context) {\n        // Implementation would check for available alternative providers\n        return true; // Placeholder\n    }\n    /**\n   * Route to alternative provider\n   */ async routeToAlternativeProvider(context) {\n        // Implementation would route to an alternative provider\n        return {\n            alternativeRoute: true,\n            result: 'Alternative provider used'\n        };\n    }\n    /**\n   * Request manual intervention\n   */ async requestManualIntervention(context) {\n        // Emit event for manual intervention request\n        (0,_lib_websocket_WorkflowWebSocketServer__WEBPACK_IMPORTED_MODULE_0__.emitWorkflowEvent)(context.workflowId, context.userId, 'log_message', {\n            level: 'warning',\n            message: `Manual intervention requested for ${context.nodeType} node`,\n            nodeId: context.nodeId,\n            error: context.error.message,\n            requiresAction: true\n        }, context.executionId);\n    }\n    /**\n   * Get error statistics for a workflow\n   */ getErrorStatistics(workflowId) {\n        const workflowErrors = Array.from(this.errorHistory.entries()).filter(([key])=>key.startsWith(workflowId)).flatMap(([_, errors])=>errors);\n        return {\n            totalErrors: workflowErrors.length,\n            errorsByNode: this.groupErrorsByNode(workflowErrors),\n            errorsByType: this.groupErrorsByType(workflowErrors),\n            recoverySuccessRate: this.calculateRecoverySuccessRate(workflowErrors)\n        };\n    }\n    groupErrorsByNode(errors) {\n        return errors.reduce((acc, error)=>{\n            acc[error.nodeId] = (acc[error.nodeId] || 0) + 1;\n            return acc;\n        }, {});\n    }\n    groupErrorsByType(errors) {\n        return errors.reduce((acc, error)=>{\n            acc[error.nodeType] = (acc[error.nodeType] || 0) + 1;\n            return acc;\n        }, {});\n    }\n    calculateRecoverySuccessRate(errors) {\n        if (errors.length === 0) return 100;\n        // This would calculate based on actual recovery outcomes\n        return 85; // Placeholder\n    }\n}\n// Export singleton instance\nconst errorRecoveryService = new ErrorRecoveryService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/workflow/ErrorRecoveryService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/workflow/WorkflowExecutionMonitor.ts":
/*!******************************************************!*\
  !*** ./src/lib/workflow/WorkflowExecutionMonitor.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WorkflowExecutionMonitor: () => (/* binding */ WorkflowExecutionMonitor),\n/* harmony export */   workflowMonitor: () => (/* binding */ workflowMonitor)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/**\n * Real-time Workflow Execution Monitor\n * Provides live updates during workflow execution with WebSocket support\n */ \nclass WorkflowExecutionMonitor {\n    static getInstance() {\n        if (!WorkflowExecutionMonitor.instance) {\n            WorkflowExecutionMonitor.instance = new WorkflowExecutionMonitor();\n        }\n        return WorkflowExecutionMonitor.instance;\n    }\n    /**\n   * Start monitoring a workflow execution\n   */ async startExecution(executionId, workflowId, userId, totalNodes) {\n        const execution = {\n            executionId,\n            workflowId,\n            status: 'starting',\n            progress: {\n                nodesCompleted: 0,\n                totalNodes,\n                percentage: 0\n            },\n            logs: [],\n            timestamp: new Date().toISOString()\n        };\n        this.activeExecutions.set(executionId, execution);\n        // Store in database\n        await this.supabase.from('workflow_executions').insert({\n            id: executionId,\n            workflow_id: workflowId,\n            user_id: userId,\n            status: 'running',\n            trigger_type: 'manual',\n            nodes_total: totalNodes,\n            started_at: new Date().toISOString()\n        });\n        this.notifySubscribers(executionId, execution);\n    }\n    /**\n   * Update execution progress\n   */ async updateProgress(executionId, nodeId, nodeType, status, message, data, duration) {\n        const execution = this.activeExecutions.get(executionId);\n        if (!execution) return;\n        // Add log entry\n        const log = {\n            id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            nodeId,\n            nodeType,\n            level: status === 'failed' ? 'error' : status === 'completed' ? 'success' : 'info',\n            message,\n            data,\n            duration,\n            timestamp: new Date().toISOString()\n        };\n        execution.logs.push(log);\n        execution.currentNodeId = nodeId;\n        execution.currentNodeType = nodeType;\n        if (status === 'completed') {\n            execution.progress.nodesCompleted++;\n            execution.progress.percentage = Math.round(execution.progress.nodesCompleted / execution.progress.totalNodes * 100);\n        }\n        execution.timestamp = new Date().toISOString();\n        // Store log in database\n        await this.supabase.from('workflow_execution_logs').insert({\n            execution_id: executionId,\n            workflow_id: execution.workflowId,\n            node_id: nodeId,\n            node_type: nodeType,\n            log_level: log.level,\n            message,\n            data,\n            duration_ms: duration,\n            created_at: new Date().toISOString()\n        });\n        this.notifySubscribers(executionId, execution);\n    }\n    /**\n   * Complete execution\n   */ async completeExecution(executionId, result, totalDuration) {\n        const execution = this.activeExecutions.get(executionId);\n        if (!execution) return;\n        execution.status = 'completed';\n        execution.result = result;\n        execution.progress.percentage = 100;\n        execution.timestamp = new Date().toISOString();\n        // Update database\n        await this.supabase.from('workflow_executions').update({\n            status: 'completed',\n            output_data: result,\n            execution_time_ms: totalDuration,\n            nodes_executed: execution.progress.nodesCompleted,\n            completed_at: new Date().toISOString()\n        }).eq('id', executionId);\n        this.notifySubscribers(executionId, execution);\n        // Clean up after 5 minutes\n        setTimeout(()=>{\n            this.activeExecutions.delete(executionId);\n            this.subscribers.delete(executionId);\n        }, 5 * 60 * 1000);\n    }\n    /**\n   * Fail execution\n   */ async failExecution(executionId, error, errorDetails) {\n        const execution = this.activeExecutions.get(executionId);\n        if (!execution) return;\n        execution.status = 'failed';\n        execution.error = error;\n        execution.timestamp = new Date().toISOString();\n        // Update database\n        await this.supabase.from('workflow_executions').update({\n            status: 'failed',\n            error_message: error,\n            error_details: errorDetails,\n            completed_at: new Date().toISOString()\n        }).eq('id', executionId);\n        this.notifySubscribers(executionId, execution);\n    }\n    /**\n   * Subscribe to execution updates\n   */ subscribe(executionId, callback) {\n        if (!this.subscribers.has(executionId)) {\n            this.subscribers.set(executionId, new Set());\n        }\n        this.subscribers.get(executionId).add(callback);\n        // Send current state if available\n        const execution = this.activeExecutions.get(executionId);\n        if (execution) {\n            callback(execution);\n        }\n        // Return unsubscribe function\n        return ()=>{\n            const subs = this.subscribers.get(executionId);\n            if (subs) {\n                subs.delete(callback);\n                if (subs.size === 0) {\n                    this.subscribers.delete(executionId);\n                }\n            }\n        };\n    }\n    /**\n   * Get execution status\n   */ getExecutionStatus(executionId) {\n        return this.activeExecutions.get(executionId) || null;\n    }\n    /**\n   * Get execution history from database\n   */ async getExecutionHistory(workflowId, userId, limit = 10) {\n        const { data, error } = await this.supabase.from('workflow_executions').select('*').eq('workflow_id', workflowId).eq('user_id', userId).order('started_at', {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error('Failed to fetch execution history:', error);\n            return [];\n        }\n        return data || [];\n    }\n    /**\n   * Get execution logs\n   */ async getExecutionLogs(executionId) {\n        const { data, error } = await this.supabase.from('workflow_execution_logs').select('*').eq('execution_id', executionId).order('created_at', {\n            ascending: true\n        });\n        if (error) {\n            console.error('Failed to fetch execution logs:', error);\n            return [];\n        }\n        return data?.map((log)=>({\n                id: log.id,\n                nodeId: log.node_id,\n                nodeType: log.node_type,\n                level: log.log_level,\n                message: log.message,\n                data: log.data,\n                duration: log.duration_ms,\n                timestamp: log.created_at\n            })) || [];\n    }\n    notifySubscribers(executionId, execution) {\n        const subscribers = this.subscribers.get(executionId);\n        if (subscribers) {\n            subscribers.forEach((callback)=>{\n                try {\n                    callback(execution);\n                } catch (error) {\n                    console.error('Error notifying subscriber:', error);\n                }\n            });\n        }\n    }\n    constructor(){\n        this.supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n        this.activeExecutions = new Map();\n        this.subscribers = new Map();\n    }\n}\nconst workflowMonitor = WorkflowExecutionMonitor.getInstance();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/workflow/WorkflowExecutionMonitor.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/workflow/WorkflowExecutor.ts":
/*!**********************************************!*\
  !*** ./src/lib/workflow/WorkflowExecutor.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WorkflowExecutor: () => (/* binding */ WorkflowExecutor),\n/* harmony export */   workflowExecutor: () => (/* binding */ workflowExecutor)\n/* harmony export */ });\n/* harmony import */ var _intelligentBrowsing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../intelligentBrowsing */ \"(rsc)/./src/lib/intelligentBrowsing.ts\");\n/* harmony import */ var _memory_RouterMemoryService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../memory/RouterMemoryService */ \"(rsc)/./src/lib/memory/RouterMemoryService.ts\");\n/* harmony import */ var _WorkflowExecutionMonitor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./WorkflowExecutionMonitor */ \"(rsc)/./src/lib/workflow/WorkflowExecutionMonitor.ts\");\n/* harmony import */ var _lib_websocket_WorkflowWebSocketServer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/websocket/WorkflowWebSocketServer */ \"(rsc)/./src/lib/websocket/WorkflowWebSocketServer.ts\");\n/* harmony import */ var _ErrorRecoveryService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ErrorRecoveryService */ \"(rsc)/./src/lib/workflow/ErrorRecoveryService.ts\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_5__);\n/**\n * Workflow Executor for Manual Build\n * Handles execution of workflows with Memory node integration\n */ \n\n\n\n\n\nclass WorkflowExecutor {\n    static getInstance() {\n        if (!WorkflowExecutor.instance) {\n            WorkflowExecutor.instance = new WorkflowExecutor();\n        }\n        return WorkflowExecutor.instance;\n    }\n    /**\n   * Execute a workflow with Memory node integration\n   */ async executeWorkflow(workflowId, userId, nodes, edges, userInput) {\n        console.log(`🚀 Starting workflow execution: ${workflowId}`);\n        // Generate unique execution ID\n        const executionId = crypto__WEBPACK_IMPORTED_MODULE_5___default().randomUUID();\n        const startTime = Date.now();\n        // Emit workflow started event\n        (0,_lib_websocket_WorkflowWebSocketServer__WEBPACK_IMPORTED_MODULE_3__.emitWorkflowEvent)(workflowId, userId, 'workflow_started', {\n            executionId,\n            totalNodes: nodes.length,\n            message: 'Workflow execution started'\n        }, executionId);\n        // Start monitoring\n        await _WorkflowExecutionMonitor__WEBPACK_IMPORTED_MODULE_2__.workflowMonitor.startExecution(executionId, workflowId, userId, nodes.length);\n        const execution = {\n            id: executionId,\n            userId,\n            nodes,\n            edges,\n            status: 'running',\n            startTime: new Date().toISOString()\n        };\n        this.activeExecutions.set(executionId, execution);\n        try {\n            // Step 1: Identify Memory nodes and connect them to other nodes\n            await _WorkflowExecutionMonitor__WEBPACK_IMPORTED_MODULE_2__.workflowMonitor.updateProgress(executionId, 'system', 'system', 'starting', 'Connecting memory nodes');\n            await this.connectMemoryNodes(workflowId, userId, nodes, edges);\n            // Step 2: Find entry point (User Request node)\n            const entryNode = nodes.find((node)=>node.type === 'userRequest');\n            if (!entryNode) {\n                throw new Error('No User Request node found in workflow');\n            }\n            await _WorkflowExecutionMonitor__WEBPACK_IMPORTED_MODULE_2__.workflowMonitor.updateProgress(executionId, 'system', 'system', 'completed', 'Memory nodes connected, starting execution');\n            // Step 3: Execute workflow starting from entry point\n            const result = await this.executeFromNode(entryNode, nodes, edges, userInput, workflowId, userId, executionId);\n            execution.status = 'completed';\n            execution.endTime = new Date().toISOString();\n            execution.result = result;\n            // Complete monitoring\n            const totalDuration = Date.now() - startTime;\n            await _WorkflowExecutionMonitor__WEBPACK_IMPORTED_MODULE_2__.workflowMonitor.completeExecution(executionId, result, totalDuration);\n            // Emit workflow completed event\n            (0,_lib_websocket_WorkflowWebSocketServer__WEBPACK_IMPORTED_MODULE_3__.emitWorkflowEvent)(workflowId, userId, 'workflow_completed', {\n                executionId,\n                result,\n                duration: totalDuration,\n                message: 'Workflow execution completed successfully'\n            }, executionId);\n            console.log(`✅ Workflow completed: ${workflowId}`);\n            return result;\n        } catch (error) {\n            execution.status = 'failed';\n            execution.endTime = new Date().toISOString();\n            execution.error = error instanceof Error ? error.message : 'Unknown error';\n            // Fail monitoring\n            await _WorkflowExecutionMonitor__WEBPACK_IMPORTED_MODULE_2__.workflowMonitor.failExecution(executionId, error instanceof Error ? error.message : 'Unknown error', {\n                error,\n                workflowId,\n                userId\n            });\n            // Emit workflow failed event\n            (0,_lib_websocket_WorkflowWebSocketServer__WEBPACK_IMPORTED_MODULE_3__.emitWorkflowEvent)(workflowId, userId, 'workflow_failed', {\n                executionId,\n                error: execution.error,\n                message: 'Workflow execution failed'\n            }, executionId);\n            console.error(`❌ Workflow failed: ${workflowId}`, error);\n            throw error;\n        }\n    }\n    /**\n   * Connect Memory nodes to their target nodes\n   */ async connectMemoryNodes(workflowId, userId, nodes, edges) {\n        const memoryNodes = nodes.filter((node)=>node.type === 'memory');\n        for (const memoryNode of memoryNodes){\n            // Find nodes that this memory connects to\n            const memoryConnections = edges.filter((edge)=>edge.source === memoryNode.id);\n            for (const connection of memoryConnections){\n                const targetNode = nodes.find((node)=>node.id === connection.target);\n                if (!targetNode) continue;\n                console.log(`🧠 Connecting Memory \"${memoryNode.data.config?.memoryName}\" to ${targetNode.type} node`);\n                // Connect based on target node type\n                switch(targetNode.type){\n                    case 'browsing':\n                        const browsingService = _intelligentBrowsing__WEBPACK_IMPORTED_MODULE_0__.IntelligentBrowsingService.getInstance();\n                        browsingService.connectMemory(memoryNode.id, workflowId, userId);\n                        break;\n                    case 'centralRouter':\n                        _memory_RouterMemoryService__WEBPACK_IMPORTED_MODULE_1__.routerMemoryService.connectMemory(memoryNode.id, workflowId, userId);\n                        break;\n                    default:\n                        console.log(`⚠️ Memory connection not implemented for node type: ${targetNode.type}`);\n                }\n            }\n        }\n    }\n    /**\n   * Execute workflow starting from a specific node\n   */ async executeFromNode(currentNode, allNodes, edges, input, workflowId, userId, executionId) {\n        console.log(`🔄 Executing node: ${currentNode.type} (${currentNode.id})`);\n        const nodeStartTime = Date.now();\n        // Update monitoring\n        if (executionId) {\n            await _WorkflowExecutionMonitor__WEBPACK_IMPORTED_MODULE_2__.workflowMonitor.updateProgress(executionId, currentNode.id, currentNode.type, 'starting', `Starting execution of ${currentNode.data.label || currentNode.type} node`);\n        }\n        // Emit node started event\n        (0,_lib_websocket_WorkflowWebSocketServer__WEBPACK_IMPORTED_MODULE_3__.emitWorkflowEvent)(workflowId, userId, 'node_started', {\n            nodeId: currentNode.id,\n            nodeType: currentNode.type,\n            nodeLabel: currentNode.data.label,\n            message: `Started executing ${currentNode.type} node`\n        }, executionId);\n        let result = input;\n        let attempt = 1;\n        const maxRetries = 3;\n        // Execute current node with error recovery\n        while(attempt <= maxRetries){\n            try {\n                // Execute current node based on its type\n                switch(currentNode.type){\n                    case 'userRequest':\n                        result = input; // Pass through user input\n                        break;\n                    case 'browsing':\n                        result = await this.executeBrowsingNode(currentNode, input, workflowId, userId, allNodes, edges);\n                        break;\n                    case 'centralRouter':\n                        result = await this.executeRouterNode(currentNode, input, allNodes, edges);\n                        break;\n                    case 'provider':\n                        result = await this.executeProviderNode(currentNode, input);\n                        break;\n                    case 'planner':\n                        result = await this.executePlannerNode(currentNode, input);\n                        break;\n                    case 'memory':\n                        // Memory nodes don't execute directly - they provide services to other nodes\n                        result = input;\n                        break;\n                    case 'classifier':\n                        result = await this.executeClassifierNode(currentNode, input, allNodes, edges);\n                        break;\n                    case 'tool':\n                        result = await this.executeToolNode(currentNode, input, allNodes, edges);\n                        break;\n                    case 'output':\n                        result = await this.executeOutputNode(currentNode, input, allNodes, edges);\n                        break;\n                    case 'vision':\n                        result = await this.executeVisionNode(currentNode, input, allNodes, edges);\n                        break;\n                    case 'roleAgent':\n                        result = await this.executeRoleAgentNode(currentNode, input, allNodes, edges);\n                        break;\n                    default:\n                        console.log(`⚠️ Execution not implemented for node type: ${currentNode.type}`);\n                        result = input;\n                }\n                break;\n            } catch (error) {\n                console.error(`❌ Node execution failed (attempt ${attempt}/${maxRetries}):`, error);\n                // Create error context\n                const errorContext = {\n                    nodeId: currentNode.id,\n                    nodeType: currentNode.type,\n                    nodeLabel: currentNode.data.label,\n                    error: error instanceof Error ? error : new Error(String(error)),\n                    attempt,\n                    maxRetries,\n                    workflowId,\n                    userId,\n                    executionId,\n                    input\n                };\n                // Attempt error recovery\n                const recoveryResult = await _ErrorRecoveryService__WEBPACK_IMPORTED_MODULE_4__.errorRecoveryService.recoverFromError(errorContext);\n                if (recoveryResult.success) {\n                    console.log(`✅ Error recovery successful: ${recoveryResult.message}`);\n                    result = recoveryResult.result;\n                    break;\n                } else if (!recoveryResult.shouldContinue) {\n                    // Recovery failed and we should not continue\n                    throw error;\n                }\n                // Increment attempt for retry\n                attempt++;\n                // If this was the last attempt, throw the error\n                if (attempt > maxRetries) {\n                    throw error;\n                }\n            }\n        }\n        // Find next nodes to execute\n        const nextEdges = edges.filter((edge)=>edge.source === currentNode.id);\n        if (nextEdges.length === 0) {\n            // End of workflow\n            return result;\n        }\n        // Update monitoring for completed node\n        if (executionId) {\n            const nodeDuration = Date.now() - nodeStartTime;\n            await _WorkflowExecutionMonitor__WEBPACK_IMPORTED_MODULE_2__.workflowMonitor.updateProgress(executionId, currentNode.id, currentNode.type, 'completed', `Completed ${currentNode.data.label || currentNode.type} node`, {\n                result\n            }, nodeDuration);\n        }\n        // Emit node completed event\n        const nodeDuration = Date.now() - nodeStartTime;\n        (0,_lib_websocket_WorkflowWebSocketServer__WEBPACK_IMPORTED_MODULE_3__.emitWorkflowEvent)(workflowId, userId, 'node_completed', {\n            nodeId: currentNode.id,\n            nodeType: currentNode.type,\n            nodeLabel: currentNode.data.label,\n            duration: nodeDuration,\n            result: typeof result === 'object' ? JSON.stringify(result) : result,\n            message: `Completed ${currentNode.type} node in ${nodeDuration}ms`\n        }, executionId);\n        // Execute next nodes (for now, just take the first one - could be enhanced for parallel execution)\n        const nextEdge = nextEdges[0];\n        const nextNode = allNodes.find((node)=>node.id === nextEdge.target);\n        if (nextNode) {\n            return await this.executeFromNode(nextNode, allNodes, edges, result, workflowId, userId, executionId);\n        }\n        return result;\n    }\n    /**\n   * Execute a Browsing node\n   */ async executeBrowsingNode(node, input, workflowId, userId, allNodes, edges) {\n        console.log(`🌐 Executing Browsing node: ${node.id}`);\n        const browsingService = _intelligentBrowsing__WEBPACK_IMPORTED_MODULE_0__.IntelligentBrowsingService.getInstance();\n        // Find connected Planner node\n        const plannerConnection = edges.find((edge)=>edge.target === node.id && edge.targetHandle === 'planner');\n        const plannerNode = plannerConnection ? allNodes.find((n)=>n.id === plannerConnection.source) : null;\n        // Find connected AI Provider node (for planner execution)\n        const aiProviderConnection = edges.find((edge)=>edge.source === node.id && edge.sourceHandle === 'output');\n        const aiProviderNode = aiProviderConnection ? allNodes.find((n)=>n.id === aiProviderConnection.target) : null;\n        // Create a browsing plan from the input\n        const plan = {\n            id: `plan_${Date.now()}`,\n            task: typeof input === 'string' ? input : 'Browse the web for information',\n            subtasks: [\n                {\n                    id: 'search_1',\n                    type: 'search',\n                    description: 'Search for relevant information',\n                    target: typeof input === 'string' ? input : 'general search',\n                    status: 'pending'\n                }\n            ],\n            estimatedTime: 5,\n            priority: 'medium'\n        };\n        // Create memory for this task\n        const memory = browsingService.createTaskMemory(plan.id);\n        // Execute the browsing plan with planner and AI provider integration\n        const { result } = await browsingService.executeBrowsingPlan(plan, memory, node.data.config, plannerNode?.id, aiProviderNode?.data.config);\n        console.log(`✅ Browsing node completed: ${node.id}`);\n        return result;\n    }\n    /**\n   * Execute a Central Router node\n   */ async executeRouterNode(node, input, allNodes, edges) {\n        // Find connected AI providers\n        const providerConnections = edges.filter((edge)=>edge.target === node.id && edge.targetHandle === 'providers');\n        const availableProviders = providerConnections.map((edge)=>allNodes.find((n)=>n.id === edge.source)).filter((n)=>n?.type === 'provider').map((n)=>n.data.config?.providerId).filter(Boolean);\n        // Find connected tools\n        const toolConnections = edges.filter((edge)=>edge.target === node.id && edge.targetHandle === 'tools');\n        const availableTools = toolConnections.map((edge)=>allNodes.find((n)=>n.id === edge.source)).filter((n)=>n?.type === 'tool').map((n)=>({\n                id: n.id,\n                type: n.data.config?.toolType,\n                config: n.data.config,\n                status: n.data.config?.connectionStatus || 'disconnected'\n            })).filter(Boolean);\n        // Find connected memory\n        const memoryConnections = edges.filter((edge)=>edge.target === node.id && edge.targetHandle === 'memory');\n        const memoryNodes = memoryConnections.map((edge)=>allNodes.find((n)=>n.id === edge.source)).filter((n)=>n?.type === 'memory');\n        if (availableProviders.length === 0) {\n            throw new Error('No AI providers connected to router');\n        }\n        // Get routing recommendation from memory\n        const recommendation = _memory_RouterMemoryService__WEBPACK_IMPORTED_MODULE_1__.routerMemoryService.getRoutingRecommendation(typeof input === 'string' ? input : JSON.stringify(input), availableProviders);\n        console.log(`🎯 Router recommendation: ${recommendation.recommendedProvider} (${recommendation.confidence * 100}% confidence)`);\n        console.log(`📝 Reason: ${recommendation.reason}`);\n        console.log(`🔧 Available tools: ${availableTools.map((t)=>t.type).join(', ')}`);\n        console.log(`🧠 Memory nodes: ${memoryNodes.length}`);\n        // Record the routing decision\n        const startTime = Date.now();\n        try {\n            // Enhanced routing result with tools and memory context\n            const result = {\n                selectedProvider: recommendation.recommendedProvider,\n                confidence: recommendation.confidence,\n                reason: recommendation.reason,\n                input: input,\n                availableTools: availableTools.filter((t)=>t.status === 'connected'),\n                memoryContext: memoryNodes.length > 0,\n                routingContext: {\n                    totalProviders: availableProviders.length,\n                    totalTools: availableTools.length,\n                    connectedTools: availableTools.filter((t)=>t.status === 'connected').length,\n                    hasMemory: memoryNodes.length > 0\n                }\n            };\n            // Record successful routing\n            await _memory_RouterMemoryService__WEBPACK_IMPORTED_MODULE_1__.routerMemoryService.recordRoutingDecision(typeof input === 'string' ? input : JSON.stringify(input), recommendation.recommendedProvider, recommendation.reason, Date.now() - startTime, true);\n            return result;\n        } catch (error) {\n            // Record failed routing\n            await _memory_RouterMemoryService__WEBPACK_IMPORTED_MODULE_1__.routerMemoryService.recordRoutingDecision(typeof input === 'string' ? input : JSON.stringify(input), recommendation.recommendedProvider, recommendation.reason, Date.now() - startTime, false);\n            throw error;\n        }\n    }\n    /**\n   * Execute a Provider node\n   */ async executeProviderNode(node, input) {\n        console.log(`🤖 Executing AI Provider: ${node.data.config?.providerId}`);\n        const config = node.data.config;\n        const providerId = config?.providerId;\n        const modelId = config?.modelId;\n        const apiKey = config?.apiKey;\n        if (!providerId || !modelId) {\n            throw new Error('AI Provider node not properly configured');\n        }\n        // Process the input based on its type\n        let processedInput = input;\n        let responseText = '';\n        if (typeof input === 'object' && input.visitedSites) {\n            // Input from browsing node - synthesize the results\n            const sites = input.visitedSites || [];\n            const successfulSites = sites.filter((site)=>site.success);\n            if (successfulSites.length === 0) {\n                responseText = 'No successful browsing results to process.';\n            } else {\n                // Create a summary of the browsing results\n                const summary = successfulSites.map((site)=>`Website: ${site.url}\\nContent: ${JSON.stringify(site.content).substring(0, 500)}...`).join('\\n\\n');\n                responseText = `Based on browsing ${successfulSites.length} websites, here's what I found:\\n\\n${summary}`;\n            }\n        } else if (typeof input === 'object' && input.plan) {\n            // Input from planner node\n            responseText = `Executed browsing plan: ${input.plan.task}\\nCompleted ${input.plan.subtasks?.length || 0} subtasks`;\n        } else {\n            // Direct text input\n            responseText = `Processed request: ${typeof input === 'string' ? input : JSON.stringify(input)}`;\n        }\n        // For now, return a structured response\n        // In a real implementation, this would call the actual AI provider API\n        const response = {\n            provider: providerId,\n            model: modelId,\n            response: responseText,\n            metadata: {\n                inputType: typeof input,\n                hasApiKey: !!apiKey,\n                timestamp: new Date().toISOString(),\n                tokenCount: responseText.length / 4 // Rough estimate\n            }\n        };\n        console.log(`✅ AI Provider response generated: ${responseText.length} characters`);\n        return response;\n    }\n    /**\n   * Execute a Planner node\n   */ async executePlannerNode(node, input) {\n        console.log(`📋 Executing Planner: ${node.data.config?.modelId}`);\n        const task = typeof input === 'string' ? input : 'Plan browsing task';\n        const maxSubtasks = node.data.config?.maxSubtasks || 5;\n        // Create detailed browsing plan\n        const plan = {\n            id: `planner_${Date.now()}`,\n            task,\n            subtasks: [\n                {\n                    id: 'search_primary',\n                    type: 'search',\n                    description: `Primary search for: ${task}`,\n                    target: task,\n                    status: 'pending',\n                    parameters: {\n                        extractionGoal: 'Find relevant websites and initial information'\n                    }\n                },\n                {\n                    id: 'search_secondary',\n                    type: 'search',\n                    description: `Secondary search for detailed information`,\n                    target: `${task} detailed information`,\n                    status: 'pending',\n                    parameters: {\n                        extractionGoal: 'Find additional sources and specific details'\n                    }\n                },\n                {\n                    id: 'analyze_results',\n                    type: 'analyze_results',\n                    description: 'Analyze search results and select best websites',\n                    target: 'search_results',\n                    status: 'pending',\n                    parameters: {\n                        extractionGoal: 'Select top websites based on relevance and authority'\n                    }\n                },\n                {\n                    id: 'navigate_selected',\n                    type: 'navigate',\n                    description: 'Visit selected websites and extract information',\n                    target: 'selected_websites',\n                    status: 'pending',\n                    parameters: {\n                        extractionGoal: 'Extract specific information related to the task'\n                    }\n                },\n                {\n                    id: 'completion_check',\n                    type: 'check_completion',\n                    description: 'Check if sufficient information has been gathered',\n                    target: 'gathered_data',\n                    status: 'pending',\n                    parameters: {\n                        extractionGoal: 'Determine if task is complete or needs more information'\n                    }\n                }\n            ].slice(0, maxSubtasks),\n            estimatedTime: Math.min(maxSubtasks * 2, 10),\n            priority: 'medium',\n            timestamp: new Date().toISOString()\n        };\n        console.log(`📋 Created detailed plan with ${plan.subtasks.length} subtasks`);\n        return plan;\n    }\n    /**\n   * Get execution status\n   */ getExecutionStatus(workflowId) {\n        return this.activeExecutions.get(workflowId) || null;\n    }\n    /**\n   * Get all active executions\n   */ getActiveExecutions() {\n        return Array.from(this.activeExecutions.values());\n    }\n    /**\n   * Execute Classifier Node - Analyzes user input to determine roles, browsing needs, and tool requirements\n   */ async executeClassifierNode(node, input, allNodes, edges) {\n        console.log(`🔍 Executing Classifier Node: ${node.id}`);\n        const config = node.data.config;\n        const userPrompt = typeof input === 'string' ? input : input?.content || input?.prompt || '';\n        if (!userPrompt.trim()) {\n            throw new Error('No user input provided for classification');\n        }\n        // Get classification API key\n        const classificationApiKey = process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;\n        if (!classificationApiKey) {\n            throw new Error('Classification service unavailable - no API key');\n        }\n        // Find connected router to get available roles\n        const routerConnections = edges.filter((edge)=>edge.source === node.id && allNodes.find((n)=>n.id === edge.target)?.type === 'centralRouter');\n        if (routerConnections.length === 0) {\n            throw new Error('Classifier must be connected to a Central Router');\n        }\n        const routerNode = allNodes.find((n)=>n.id === routerConnections[0].target);\n        const availableRoles = this.getAvailableRolesFromRouter(routerNode, allNodes, edges);\n        // Perform comprehensive classification\n        const classification = await this.performComprehensiveClassification(userPrompt, availableRoles, classificationApiKey);\n        console.log(`✅ Classification complete:`, classification);\n        return {\n            ...input,\n            classification,\n            classifiedRoles: classification.roles,\n            needsBrowsing: classification.needsBrowsing,\n            needsTools: classification.needsTools,\n            isMultiRole: classification.isMultiRole,\n            reasoning: classification.reasoning\n        };\n    }\n    /**\n   * Execute Tool Node - Handles external tool integrations\n   */ async executeToolNode(node, input, allNodes, edges) {\n        console.log(`🔧 Executing Tool Node: ${node.id}`);\n        const config = node.data.config;\n        if (!config.toolType) {\n            throw new Error('Tool type not configured');\n        }\n        // For now, only web browsing is implemented\n        if (config.toolType === 'web_browsing') {\n            // Web browsing is handled by the browsing node, not tool node\n            console.log(`🌐 Web browsing detected - delegating to browsing system`);\n            return {\n                ...input,\n                toolResult: {\n                    type: 'web_browsing',\n                    status: 'delegated_to_browsing_node',\n                    message: 'Web browsing handled by dedicated browsing node'\n                }\n            };\n        }\n        // For other tools, return not implemented for now\n        return {\n            ...input,\n            toolResult: {\n                type: config.toolType,\n                status: 'not_implemented',\n                message: `${config.toolType} integration coming soon`\n            }\n        };\n    }\n    /**\n   * Execute Output Node - Handles final response streaming and formatting\n   */ async executeOutputNode(node, input, allNodes, edges) {\n        console.log(`📤 Executing Output Node: ${node.id}`);\n        const config = node.data.config;\n        const outputFormat = config.outputFormat || 'stream';\n        const chunkSize = config.chunkSize || 15;\n        // Extract the final response from input\n        const finalResponse = input?.response || input?.content || input?.result || JSON.stringify(input);\n        console.log(`📤 Output Node streaming response (${finalResponse.length} chars)`);\n        // For workflow execution, we'll store the final result\n        // The actual streaming will be handled by the workflow API endpoint\n        return {\n            ...input,\n            finalOutput: finalResponse,\n            outputConfig: {\n                format: outputFormat,\n                chunkSize,\n                timestamp: new Date().toISOString()\n            },\n            isComplete: true\n        };\n    }\n    /**\n   * Execute Vision Node - Handles multimodal AI processing\n   */ async executeVisionNode(node, input, allNodes, edges) {\n        console.log(`👁️ Executing Vision Node: ${node.id}`);\n        const config = node.data.config;\n        if (!config.providerId || !config.modelId) {\n            throw new Error('Vision node not properly configured - missing provider or model');\n        }\n        // Check if input contains images\n        const hasImages = this.detectImagesInInput(input);\n        if (!hasImages) {\n            console.log(`⚠️ No images detected in input for vision node`);\n            return {\n                ...input,\n                visionResult: {\n                    status: 'no_images',\n                    message: 'No images found in input'\n                }\n            };\n        }\n        // For now, pass through to provider node execution\n        // The actual vision processing will be handled by the provider\n        console.log(`👁️ Vision processing delegated to provider: ${config.providerId}`);\n        return {\n            ...input,\n            visionProcessed: true,\n            visionProvider: config.providerId,\n            visionModel: config.modelId\n        };\n    }\n    /**\n   * Execute Role Agent Node - Handles role assignment and configuration\n   */ async executeRoleAgentNode(node, input, allNodes, edges) {\n        console.log(`👤 Executing Role Agent Node: ${node.id}`);\n        const config = node.data.config;\n        if (!config.roleName) {\n            throw new Error('Role agent not configured - missing role name');\n        }\n        // Role agents don't execute directly - they provide role information to connected providers\n        console.log(`👤 Role agent configured: ${config.roleName} (${config.roleType})`);\n        return {\n            ...input,\n            assignedRole: {\n                name: config.roleName,\n                type: config.roleType,\n                description: config.roleDescription,\n                nodeId: node.id\n            }\n        };\n    }\n    /**\n   * Get available roles from connected router node\n   */ getAvailableRolesFromRouter(routerNode, allNodes, edges) {\n        // Find all provider nodes connected to the router\n        const providerConnections = edges.filter((edge)=>edge.target === routerNode.id && allNodes.find((n)=>n.id === edge.source)?.type === 'provider');\n        const roles = [];\n        // Get roles from connected providers\n        providerConnections.forEach((connection)=>{\n            const providerNode = allNodes.find((n)=>n.id === connection.source);\n            if (providerNode) {\n                // Find role agents connected to this provider\n                const roleConnections = edges.filter((edge)=>edge.target === providerNode.id && edge.targetHandle === 'role' && allNodes.find((n)=>n.id === edge.source)?.type === 'roleAgent');\n                roleConnections.forEach((roleConnection)=>{\n                    const roleNode = allNodes.find((n)=>n.id === roleConnection.source);\n                    if (roleNode) {\n                        const roleConfig = roleNode.data.config;\n                        roles.push({\n                            id: roleNode.id,\n                            name: roleConfig.roleName || 'Unknown Role',\n                            description: roleConfig.roleDescription || 'No description'\n                        });\n                    }\n                });\n            }\n        });\n        // If no specific roles found, add default roles\n        if (roles.length === 0) {\n            roles.push({\n                id: 'general',\n                name: 'General Assistant',\n                description: 'General purpose AI assistant'\n            });\n        }\n        return roles;\n    }\n    /**\n   * Perform comprehensive classification using Gemini\n   */ async performComprehensiveClassification(prompt, availableRoles, classificationApiKey) {\n        const roleInfoForPrompt = availableRoles.map((r)=>`- Role ID: \"${r.id}\", Name: \"${r.name}\", Description: \"${r.description}\"`).join('\\n');\n        const systemPrompt = `You are RouKey's Comprehensive Task Classifier. Analyze the user request and determine:\n\n1. ROLES NEEDED: Which specialized roles should handle this task\n2. BROWSING REQUIRED: Does this need web browsing/research?\n3. TOOLS REQUIRED: Does this need external tools (docs, sheets, etc.)?\n4. VISION REQUIRED: Does this involve image analysis?\n\nAvailable Roles:\n${roleInfoForPrompt}\n\nRespond with JSON:\n{\n  \"isMultiRole\": boolean,\n  \"roles\": [{\"roleId\": \"role_id\", \"confidence\": 0.9, \"executionOrder\": 1}],\n  \"needsBrowsing\": boolean,\n  \"needsTools\": boolean,\n  \"needsVision\": boolean,\n  \"toolTypes\": [\"google_docs\", \"web_browsing\"],\n  \"reasoning\": \"explanation of classification decisions\"\n}\n\nExamples:\n- \"write a fairy tale\" → single role (creative_writing), no browsing/tools\n- \"research latest AI trends and create a report\" → single role (research), needs browsing + docs\n- \"find messi's next match\" → single role (general), needs browsing\n- \"brainstorm game ideas and code a snake game\" → multi-role (brainstorming + coding), no browsing\n- \"analyze this image and write a description\" → single role (general), needs vision`;\n        try {\n            const response = await fetch('https://generativelanguage.googleapis.com/v1beta/openai/chat/completions', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${classificationApiKey}`,\n                    'Connection': 'keep-alive',\n                    'User-Agent': 'RoKey/1.0 (Workflow-Classification)'\n                },\n                body: JSON.stringify({\n                    model: 'gemini-2.0-flash-lite',\n                    messages: [\n                        {\n                            role: 'system',\n                            content: systemPrompt\n                        },\n                        {\n                            role: 'user',\n                            content: `Task: ${prompt}`\n                        }\n                    ],\n                    temperature: 0.1,\n                    max_tokens: 500\n                })\n            });\n            if (!response.ok) {\n                throw new Error(`Classification API error: ${response.status}`);\n            }\n            const result = await response.json();\n            const content = result.choices?.[0]?.message?.content;\n            if (!content) {\n                throw new Error('No classification result received');\n            }\n            const parsed = JSON.parse(content);\n            // Validate and filter roles against available roles\n            if (parsed.roles) {\n                parsed.roles = parsed.roles.filter((role)=>availableRoles.some((availableRole)=>availableRole.id === role.roleId));\n            }\n            return parsed;\n        } catch (error) {\n            console.error('Classification error:', error);\n            // Fallback classification\n            return {\n                isMultiRole: false,\n                roles: [\n                    {\n                        roleId: availableRoles[0]?.id || 'general',\n                        confidence: 0.5,\n                        executionOrder: 1\n                    }\n                ],\n                needsBrowsing: prompt.toLowerCase().includes('search') || prompt.toLowerCase().includes('find'),\n                needsTools: false,\n                needsVision: false,\n                toolTypes: [],\n                reasoning: 'Fallback classification due to API error'\n            };\n        }\n    }\n    /**\n   * Detect if input contains images for vision processing\n   */ detectImagesInInput(input) {\n        if (typeof input === 'string') return false;\n        // Check for image URLs or base64 images in various input formats\n        const inputStr = JSON.stringify(input).toLowerCase();\n        return inputStr.includes('image_url') || inputStr.includes('data:image') || inputStr.includes('.jpg') || inputStr.includes('.png') || inputStr.includes('.jpeg') || inputStr.includes('.gif') || inputStr.includes('.webp');\n    }\n    constructor(){\n        this.activeExecutions = new Map();\n    }\n}\n// Export singleton instance\nconst workflowExecutor = WorkflowExecutor.getInstance();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/workflow/WorkflowExecutor.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmanual-build%2Fexecute-workflow%2Froute&page=%2Fapi%2Fmanual-build%2Fexecute-workflow%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmanual-build%2Fexecute-workflow%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();